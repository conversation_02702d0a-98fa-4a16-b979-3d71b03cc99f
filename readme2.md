这是一个社交软件的动态模块的服务端项目，大致的需求如下：
用户可以在发布动态，发布时需要选择动态的可见性。有四个选项，分别是 公开、私密、部分人可见、部分人不可见，这四个选项彼此互斥，且必须在四个选项中选择其中一项。
其中，公开代表全部好友可见；私密代表仅自己可见；部分人可见类似白名单，只有选择了的用户组内的组成员可见，可选择多个用户组；部分人不可见类似黑名单，只有选择了的用户组内的组成员不可见，可选择多个用户组。

为了实现这个需求，我采用了postgreSQL数据库，并设计了如下的表结构：

1, 动态表 (feeds)
```sql
CREATE TABLE feeds (
    feed_id BIGINT PRIMARY KEY,
    feed_qid VARCHAR(50),
    content TEXT,
    vis_type INT, // 可见性类型 0:私密 1:公开 2:部分人可见 3:部分人不可见
    public BOOLEAN, // 是否是公开动态(同步到好友圈)
    location JSONB,
    created_at BIGINT,
    update_at BIGINT,
    status INT // 0:未审核 1:机审通过 2:人审通过 -1:机审不通过 -2:人审不通过 -3:删除
    extra JSONB // 扩展字段 
);
```

2, 动态分组表 (user_feed_groups)
```sql
CREATE TABLE user_feed_groups (
    group_id VARCHAR(50) PRIMARY KEY,
    group_name VARCHAR(50),
    owner_qid VARCHAR(50),
    group_type INT, // 分组类型 0:临时分组 1:固定分组
    created_at BIGINT,
    update_at BIGINT,
    status INT // 0:正常 1:删除
);
```

3, 分组成员表 (user_feed_group_members)
```sql
CREATE TABLE user_feed_group_members (
    group_id VARCHAR(50),
    owner_qid VARCHAR(50),
    member_qid VARCHAR(50),
    created_at BIGINT
);
```

4, 动态可见性表 (feed_visibilities)
```sql
CREATE TABLE feed_visibilities (
    feed_id VARCHAR(50),
    group_id VARCHAR(50),
    vis_type INT, // 规则 0:私密 1:公开 2:白名单 3:黑名单
    created_at BIGINT // 动态创建时间
);
```

5，用户可见动态表 (user_visible_feeds)
```sql
CREATE TABLE user_visible_feeds (
    qid VARCHAR(50),
    feed_id VARCHAR(50),
    feed_qid VARCHAR(50),
    created_at BIGINT
);
```

6, 动态媒体表 (feed_medias)
```sql
CREATE TABLE feed_medias (
    media_id VARCHAR(50) PRIMARY KEY,
    feed_id VARCHAR(50),
    feed_qid VARCHAR(50),
    media_type INT, // 1:图片 2:视频
    media_order INT, // 顺序
    media_object_key VARCHAR(50), // 存储在s3的objectKey
    created_at BIGINT,
    thumbhash VARCHAR(50),
    file_size INT,
    duration INT // 视频/音频时长 单位毫秒
    width INT,
    height INT,
    exif JSONB, // exif信息
    extra JSONB // 扩展字段 
);
```

7, 动态评论表
```sql
CREATE TABLE feed_comments (
    comment_id VARCHAR(50) PRIMARY KEY, // 评论id
    feed_id VARCHAR(50), // 动态id
    feed_qid VARCHAR(50), // 动态发布者qid
    qid VARCHAR(50), // 评论者qid
    content TEXT, // 评论内容
    reply_to_comment_id VARCHAR(50), // 回复的【哪条】评论
    reply_to_qid VARCHAR(50), // 回复的【哪个人】的评论
    created_at BIGINT, // 评论时间
    status INT // 0:正常 1:删除
);
```

8，点赞表
```sql
CREATE TABLE feed_likes (
    feed_id VARCHAR(50),
    feed_qid VARCHAR(50), // 动态发布者qid
    qid VARCHAR(50),
    created_at BIGINT
);
```

9，用户关系表
```sql
CREATE TABLE user_relations (
    qid VARCHAR(50),
    target_qid VARCHAR(50),
    relationship_type INT, // 1:好友 2:拉黑
    created_at BIGINT
);
```

首先，发布动态时需要把动态内容插入到feeds表中，其中动态id使用snowflake算法生成, 然后根据可见性类型插入到feed_visibilities表中。

当可见性为私密时，插入feed_visibilities表时，feed_id为动态id, vis_type为0，group_id为self(只插入一条);
当可见性为公开时，插入feed_visibilities表时，feed_id为动态id, vis_type为1，group_id为all(只插入一条);
当可见性为部分人可见时，插入feed_visibilities表时，feed_id为动态id, vis_type为2，group_id为选择的分组id(根据用户选择的用户组数量，可插入多条);
当可见性为部分人不可见时，插入feed_visibilities表时，feed_id为动态id, vis_type为3，group_id为选择的分组id(根据用户选择的用户组数量，可插入多条);

其次，发布动态时可选附带一些视频和图片，如果用户选择了图片和视频，则需要把图片和视频的信息插入到feed_medias表中。

最后，发布动态后，我们会异步的把动态id存储到用户可见动态表 (user_visible_feeds)，获取用户可见的动态列表时，都会从这个表里查询。
在当前项目中不需要操作把数据存入user_visible_feeds表中，因为这个任务量较大，时间可能会很长，所以只需要封装一个api的调用即可。
我会在另外一个项目中实现数据的存储，但接口需要传递必要的参数，相当于在本项目中只需要通过api提交一个任务即可。

现在需要在本项目中的controllers文件夹中实现如下接口：
1, 发布动态
2, 修改动态(包含修改动态内容和动态可见性)
3, 删除动态
4, 获取用户A所有可见的动态
5, 获取用户A查看用户B的所有可见的的动态
6, 查看动态详情
7, 创建用户组
8，删除用户组
9，修改用户组名称
10，添加用户到用户组(支持批量增加)
11, 从用户组中移除用户(支持批量删除)
12, 获取用户组列表


