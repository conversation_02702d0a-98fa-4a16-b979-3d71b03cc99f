package utils

import (
	crand "crypto/rand"
	"fmt"
	"io"
	"math/rand"
	"strings"
)

// ValidatePhoneNumber performs basic phone number validation
func ValidatePhoneNumber(phone string) bool {
	// Basic validation: should be 11 digits starting with 1
	if len(phone) != 11 {
		return false
	}

	// Check if it starts with 1 (the Chinese mobile number format)
	if phone[0] != '1' {
		return false
	}

	// Check if all characters are digits
	for _, char := range phone {
		if char < '0' || char > '9' {
			return false
		}
	}

	return true
}

// GenerateOTP generates a random 6-digit numeric OTP
func GenerateOTP() string {
	// Generate a 6-digit number [000001 to 999999]
	otp := rand.Intn(999998) + 1
	return fmt.Sprintf("%06d", otp)
}

func MaskPhoneNumber(phone string) string {
	if len(phone) < 7 {
		return phone
	}
	// 把手机号的中间4位变为*
	return phone[:3] + "****" + phone[7:]
}

var table = [...]byte{'1', '2', '3', '4', '5', '6', '7', '8', '9', '0', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'}

func GetRandomString(count int) string {
	b := make([]byte, count)
	_, _ = io.ReadAtLeast(crand.Reader, b, count)
	tb := len(table)

	for i := 0; i < len(b); i++ {
		b[i] = table[int(b[i])%tb]
	}
	return string(b)
}

// SplitSlice 把大数组切分成多个小数组
func SplitSlice(s []string, max int) [][]string {
	var result [][]string
	for i := 0; i < len(s); i += max {
		end := i + max
		if end > len(s) {
			end = len(s)
		}
		result = append(result, s[i:end])
	}
	return result
}

// IsImageType 判断文件类型是否为图片
func IsImageType(filetype string) bool {
	// 常见的图片格式
	imageTypes := []string{
		"jpg", "jpeg", "png", "gif", "bmp", "webp",
		"svg", "tiff", "tif", "ico", "heic", "heif",
		"raw", "cr2", "nef", "arw", "dng",
	}

	// 转换为小写进行比较
	filetype = strings.ToLower(strings.TrimSpace(filetype))

	// 移除可能的点号前缀
	filetype = strings.TrimPrefix(filetype, ".")

	// 检查是否包含图片类型关键词
	for _, imageType := range imageTypes {
		if strings.Contains(filetype, imageType) {
			return true
		}
	}

	return false
}

// IsVideoType 判断文件类型是否为视频
func IsVideoType(filetype string) bool {
	// 常见的视频格式
	videoTypes := []string{
		// 常用格式
		"mp4", "avi", "mov", "wmv", "flv", "webm", "mkv",
		// MPEG系列
		"mpg", "mpeg", "m4v", "3gp", "3g2",
		// 其他常见格式
		"rm", "rmvb", "asf", "divx", "xvid", "vob",
		// 高清格式
		"ts", "mts", "m2ts", "f4v", "ogv",
		// 专业格式
		"mxf", "prores", "dnxhd", "r3d", "braw",
		// 流媒体格式
		"m3u8", "hls", "dash", "mpd",
	}

	// 转换为小写进行比较
	filetype = strings.ToLower(strings.TrimSpace(filetype))

	// 移除可能的点号前缀
	filetype = strings.TrimPrefix(filetype, ".")

	// 检查是否包含视频类型关键词
	for _, videoType := range videoTypes {
		if strings.Contains(filetype, videoType) {
			return true
		}
	}

	return false
}
