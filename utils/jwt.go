package utils

import (
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/sirupsen/logrus"
)

// JWT configuration constants
const (
	JWTSecretKey   = "9DCFF692700F4C2B9422453C6FCF1D70"
	JWTIssuer      = "qing-app"
	JWTTokenExpire = 30 * 24 * time.Hour // 30 days
)

// JWTClaims Custom claims structure
type JWTClaims struct {
	Qid      string `json:"qid"`
	DeviceID string `json:"device_id"`
	jwt.RegisteredClaims
}

// GenerateJWT generates a JWT token for a user
func GenerateJWT(qid string, deviceId string) (string, int64, error) {
	expireTime := time.Now().Add(JWTTokenExpire)
	claims := JWTClaims{
		Qid:      qid,
		DeviceID: deviceId,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    JWTIssuer,
			Subject:   qid,
			ExpiresAt: jwt.NewNumericDate(expireTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(JWTSecretKey))
	if err != nil {
		return "", 0, fmt.Errorf("failed to generate JWT token: %w", err)
	}

	return tokenString, expireTime.UnixMilli(), nil
}

// ValidateJWT validates a JWT token and returns the claims
func ValidateJWT(tokenString string) (*JWTClaims, error) {
	// Remove "Bearer " prefix if present
	//tokenString = strings.TrimPrefix(tokenString, "Bearer ")

	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(JWTSecretKey), nil
	})

	if err != nil {
		logrus.WithError(err).Error("Failed to parse JWT token")
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, errors.New("invalid token claims")
	}

	// Additional validation
	if claims.Qid == "" {
		return nil, errors.New("invalid QID in token")
	}

	return claims, nil
}
