package utils

import (
	"errors"

	gonanoid "github.com/matoous/go-nanoid/v2"
)

func GenerateQID() (string, error) {
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ", 14)
		if err == nil {
			return "QID_" + id, nil
		}
	}
	return "", errors.New("QID generation failure")
}

func GenerateFeedID() string {
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 14)
		if err == nil {
			return "feed_" + id
		}
	}
	return "feed_" + GetRandomString(14)
}

func GenerateUserFeedGroupID() string {
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 14)
		if err == nil {
			return "fg_" + id
		}
	}

	return "fg_" + GetRandomString(14)
}
