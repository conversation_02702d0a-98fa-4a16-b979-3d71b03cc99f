package utils

import "errors"

func GeneratePendingReviewTaskID() int64 {
	return GenerateSnowflakeID()
}
func GenerateMediaID() int64 {
	return GenerateSnowflakeID()
}

func GenerateFeedID() int64 {
	return GenerateSnowflakeID()
}

func GenerateFeedGroupID() int64 {
	return GenerateSnowflakeID()
}

func GenerateFeedCommentID() int64 {
	return GenerateSnowflakeID()
}
func EncodeQID(qid int64) (string, error) {
	return qidGenerator11.Encode([]uint64{uint64(qid)})
}

func DecodeQID(qid string) (int64, error) {
	result := qidGenerator11.Decode(qid)
	if len(result) > 0 {
		return int64(result[0]), nil
	}
	return 0, errors.New("invalid qid")
}

func EncodeMediaID(mediaId int64) (string, error) {
	return defaultGenerator16.Encode([]uint64{uint64(mediaId)})
}

func DecodeMediaID(mediaId string) (int64, error) {
	result := defaultGenerator16.Decode(mediaId)
	if len(result) > 0 {
		return int64(result[0]), nil
	}
	return 0, errors.New("invalid mediaId")
}

func EncodeFeedID(feedId int64) (string, error) {
	return defaultGenerator16.Encode([]uint64{uint64(feedId)})
}

func DecodeFeedID(feedId string) (int64, error) {
	result := defaultGenerator16.Decode(feedId)
	if len(result) > 0 {
		return int64(result[0]), nil
	}
	return 0, errors.New("invalid feedId")
}

func EncodeFeedCommentID(commentID int64) (string, error) {
	return defaultGenerator16.Encode([]uint64{uint64(commentID)})
}

func DecodeFeedCommentID(commentID string) (int64, error) {
	result := defaultGenerator16.Decode(commentID)
	if len(result) > 0 {
		return int64(result[0]), nil
	}
	return 0, errors.New("invalid commentID")
}

func EncodeFeedGroupID(feedGroupID int64) (string, error) {
	return defaultGenerator16.Encode([]uint64{uint64(feedGroupID)})
}

func DecodeFeedGroupID(feedGroupID string) (int64, error) {
	result := defaultGenerator16.Decode(feedGroupID)
	if len(result) > 0 {
		return int64(result[0]), nil
	}
	return 0, errors.New("invalid feedGroupID")
}
