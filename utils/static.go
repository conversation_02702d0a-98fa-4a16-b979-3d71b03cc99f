package utils

import (
	"fmt"
	"qing-feeds/config"
)

func GetOriginAvatarUrl(objectKey string) string {
	cfg := config.GetConfig()
	return fmt.Sprintf("https://%v/%v", cfg.StaticResource.Domain, objectKey)
}

func GetMiddleAvatarUrl(objectKey string) string {
	cfg := config.GetConfig()
	return fmt.Sprintf("https://%v/%v?w=1000&h=1000", cfg.StaticResource.Domain, objectKey)
}

func GetSmallAvatarUrl(objectKey string) string {
	cfg := config.GetConfig()
	return fmt.Sprintf("https://%v/%v?w=500&h=500", cfg.StaticResource.Domain, objectKey)
}

func GetOriginFeedMediaUrl(objectKey string) string {
	cfg := config.GetConfig()
	return fmt.Sprintf("https://%v/%v", cfg.StaticResource.Domain, objectKey)
}

func GetOriginFeedVideoCoverUrl(objectKey string) string {
	cfg := config.GetConfig()
	return fmt.Sprintf("https://%v/%v", cfg.StaticResource.Domain, objectKey)
}

func GetMiddleFeedImageUrl(objectKey string) string {
	cfg := config.GetConfig()
	return fmt.Sprintf("https://%v/%v?w=1000", cfg.StaticResource.Domain, objectKey)
}

func GetSmallFeedImageUrl(objectKey string) string {
	cfg := config.GetConfig()
	return fmt.Sprintf("https://%v/%v?w=500", cfg.StaticResource.Domain, objectKey)
}

func GetMiddleFeedVideoUrl(objectKey string) string {
	cfg := config.GetConfig()
	return fmt.Sprintf("https://%v/%v", cfg.StaticResource.Domain, objectKey)
}
