package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"

	"github.com/sirupsen/logrus"
)

// 对称加密密钥（32字节用于AES-256）
// 在生产环境中，这应该从环境变量或配置文件中读取

const EncryptionKey = "6EFB2ECCA3C8417F8C50181EBDB31A29" // 32字节密钥

// EncryptToken 使用AES-256-GCM对JWT令牌进行对称加密
func EncryptToken(token string) (string, error) {
	// 创建AES cipher
	block, err := aes.NewCipher([]byte(EncryptionKey))
	if err != nil {
		logrus.WithError(err).Error("创建AES cipher失败")
		return "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		logrus.WithError(err).Error("创建GCM模式失败")
		return "", err
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		logrus.WithError(err).Error("生成随机nonce失败")
		return "", err
	}

	// 加密令牌
	ciphertext := gcm.Seal(nonce, nonce, []byte(token), nil)

	// 使用Base64编码返回
	encryptedToken := base64.StdEncoding.EncodeToString(ciphertext)

	logrus.WithFields(logrus.Fields{
		"original_length":  len(token),
		"encrypted_length": len(encryptedToken),
	}).Debug("JWT令牌加密成功")

	return encryptedToken, nil
}

// DecryptToken 使用AES-256-GCM对加密的JWT令牌进行解密
func DecryptToken(encryptedToken string) (string, error) {
	// Base64解码
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedToken)
	if err != nil {
		logrus.WithError(err).Error("Base64解码失败")
		return "", err
	}

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(EncryptionKey))
	if err != nil {
		logrus.WithError(err).Error("创建AES cipher失败")
		return "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		logrus.WithError(err).Error("创建GCM模式失败")
		return "", err
	}

	// 检查密文长度
	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", errors.New("密文长度不足")
	}

	// 提取nonce和密文
	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	// 解密
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		logrus.WithError(err).Error("解密失败")
		return "", err
	}

	token := string(plaintext)

	logrus.WithFields(logrus.Fields{
		"encrypted_length": len(encryptedToken),
		"decrypted_length": len(token),
	}).Debug("JWT令牌解密成功")

	return token, nil
}

//// ValidateEncryptionKey 验证加密密钥是否有效
//func ValidateEncryptionKey() error {
//	if len(EncryptionKey) != 32 {
//		return errors.New("加密密钥长度必须为32字节")
//	}
//	return nil
//}

// TestEncryption 测试加密解密功能
func TestEncryption(testData string) error {
	// 加密
	encrypted, err := EncryptToken(testData)
	if err != nil {
		return err
	}

	// 解密
	decrypted, err := DecryptToken(encrypted)
	if err != nil {
		return err
	}

	// 验证
	if decrypted != testData {
		return errors.New("加密解密测试失败：数据不匹配")
	}

	logrus.WithFields(logrus.Fields{
		"original":  testData,
		"encrypted": encrypted,
		"decrypted": decrypted,
	}).Debug("加密解密测试成功")

	return nil
}
