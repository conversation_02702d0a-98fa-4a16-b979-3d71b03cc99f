package utils

import (
	"github.com/sqids/sqids-go"
)

var qidGenerator11 *sqids.Sqids
var defaultGenerator16 *sqids.Sqids

func init() {
	qidGenerator11, _ = sqids.New(sqids.Options{
		Alphabet:  "891736245", // 这个就是盐，换了字符的顺序会导致盐的变化
		MinLength: 11,
	})
	defaultGenerator16, _ = sqids.New(sqids.Options{
		Alphabet:  "KY0Ft5yasH7kBiMw1ruLlXzd94RQmJfjhTnc3VSNxqG8UODgZb6EoAIW2vepPC", // 0-9a-zA-Z 这个就是盐，换了字符的顺序会导致盐的变化
		MinLength: 16,
	})
}
