package utils

import (
	"errors"
	"regexp"
	"unicode"

	"golang.org/x/crypto/bcrypt"
)

// Password validation constants
const (
	MinPasswordLength = 8
	MaxPasswordLength = 128
	BcryptCost        = 12 // Recommended cost for bcrypt
)

// Password strength requirements
var (
	ErrPasswordTooShort = errors.New("password must be at least 8 characters long")
	ErrPasswordTooLong  = errors.New("password must be no more than 128 characters long")
	ErrPasswordWeak     = errors.New("password must contain at least one letter and one number")
)

// HashPassword hashes a password using bcrypt
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), BcryptCost)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// VerifyPassword verifies a password against its hash
func VerifyPassword(password, hash string) error {
	return bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
}

// ValidatePasswordStrength validates password strength requirements
func ValidatePasswordStrength(password string) error {
	// Check length
	if len(password) < MinPasswordLength {
		return ErrPasswordTooShort
	}

	if len(password) > MaxPasswordLength {
		return ErrPasswordTooLong
	}

	// Check for at least one letter and one number
	hasLetter := false
	hasNumber := false

	for _, char := range password {
		if unicode.IsLetter(char) {
			hasLetter = true
		}
		if unicode.IsNumber(char) {
			hasNumber = true
		}
		if hasLetter && hasNumber {
			break
		}
	}

	if !hasLetter || !hasNumber {
		return ErrPasswordWeak
	}

	return nil
}

// ValidatePasswordFormat performs additional format validation
func ValidatePasswordFormat(password string) error {
	// Check for common weak patterns
	weakPatterns := []string{
		`^(.)\1+$`, // All same character (e.g., "aaaaaaaa")
		`^(012|123|234|345|456|567|678|789|890)+`,                                                             // Sequential numbers
		`^(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)+`, // Sequential letters
	}

	for _, pattern := range weakPatterns {
		matched, _ := regexp.MatchString(pattern, password)
		if matched {
			return errors.New("password contains weak patterns")
		}
	}

	return nil
}

// IsPasswordSecure performs comprehensive password validation
func IsPasswordSecure(password string) error {
	// Basic strength validation
	if err := ValidatePasswordStrength(password); err != nil {
		return err
	}

	// Format validation
	if err := ValidatePasswordFormat(password); err != nil {
		return err
	}

	return nil
}
