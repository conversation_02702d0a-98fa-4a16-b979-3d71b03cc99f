package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
)

// APIClient HTTP客户端封装
type APIClient struct {
	baseURL    string
	httpClient *http.Client
	timeout    time.Duration
}

// NewAPIClient 创建新的API客户端
func NewAPIClient(baseURL string, timeout time.Duration) *APIClient {
	return &APIClient{
		baseURL: baseURL,
		httpClient: &http.Client{
			Timeout: timeout,
		},
		timeout: timeout,
	}
}

// UserVisibleFeedsTaskRequest 用户可见动态任务请求
type UserVisibleFeedsTaskRequest struct {
	FeedID   string   `json:"feed_id"`
	FeedQid  string   `json:"feed_qid"`
	VisType  string   `json:"vis_type"`
	GroupIDs []string `json:"group_ids,omitempty"`
}

// UserVisibleFeedsTaskResponse 用户可见动态任务响应
type UserVisibleFeedsTaskResponse struct {
	TaskID string `json:"task_id"`
	Status string `json:"status"`
}

// SubmitUserVisibleFeedsTask 提交用户可见动态任务
func (c *APIClient) SubmitUserVisibleFeedsTask(req UserVisibleFeedsTaskRequest) (*UserVisibleFeedsTaskResponse, error) {
	url := fmt.Sprintf("%s/api/tasks/user-visible-feeds", c.baseURL)

	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response UserVisibleFeedsTaskResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &response, nil
}

// 全局API客户端实例
var (
	defaultAPIClient *APIClient
)

// InitAPIClient 初始化默认API客户端
func InitAPIClient(baseURL string, timeout time.Duration) {
	defaultAPIClient = NewAPIClient(baseURL, timeout)
}

// SubmitUserVisibleFeedsTaskAsync 异步提交用户可见动态任务
func SubmitUserVisibleFeedsTaskAsync(feedID string, feedQid string, visType string, groupIDs []string) {
	if defaultAPIClient == nil {
		logrus.Error("API client not initialized")
		return
	}

	go func() {
		req := UserVisibleFeedsTaskRequest{
			FeedID:   feedID,
			FeedQid:  feedQid,
			VisType:  visType,
			GroupIDs: groupIDs,
		}

		resp, err := defaultAPIClient.SubmitUserVisibleFeedsTask(req)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"feed_id":   feedID,
				"feed_qid":  feedQid,
				"vis_type":  visType,
				"group_ids": groupIDs,
			}).Error("Failed to submit user visible feeds task")
			return
		}

		logrus.WithFields(logrus.Fields{
			"feed_id":  feedID,
			"feed_qid": feedQid,
			"task_id":  resp.TaskID,
			"status":   resp.Status,
		}).Info("Successfully submitted user visible feeds task")
	}()
}
