package database

import (
	"context"
	"fmt"
	"qing-feeds/config"
	"time"

	"github.com/redis/rueidis"
	"github.com/sirupsen/logrus"
)

var theRedisClient rueidis.Client

// initRedis initializes Redis connection
func initRedis(cfg *config.Config) error {
	// Build Redis connection options
	options := rueidis.ClientOption{
		InitAddress: []string{cfg.Redis.Addr},
		Username:    cfg.Redis.Username,
		Password:    cfg.Redis.Password,
		SelectDB:    cfg.Redis.DB,
	}

	// Create a Redis client
	client, err := rueidis.NewClient(options)
	if err != nil {
		return fmt.Errorf("failed to create Redis client: %w", err)
	}

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Do(ctx, client.B().Ping().Build()).Error(); err != nil {
		return fmt.Errorf("failed to ping Redis: %w", err)
	}

	theRedisClient = client

	logrus.WithFields(logrus.Fields{}).Info("Redis connection established")

	return nil
}

// CloseRedis closes the Redis connection
func CloseRedis() {
	if theRedisClient != nil {
		logrus.Info("Closing Redis connection")
		theRedisClient.Close()
	}
}

// HealthCheckRedis checks if Redis connection is healthy
func HealthCheckRedis() error {
	if theRedisClient == nil {
		return fmt.Errorf("redis client is nil")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := theRedisClient.Do(ctx, theRedisClient.B().Ping().Build()).Error(); err != nil {
		return fmt.Errorf("redis ping failed: %w", err)
	}

	return nil
}

// GetRedisClient returns the Redis client
func GetRedisClient() rueidis.Client {
	return theRedisClient
}

// SetCache sets a value in Redis with expiration
func SetCache(key string, value string, expiration time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if expiration > 0 {
		cmd := theRedisClient.B().Set().Key(key).Value(value).Ex(expiration).Build()
		return theRedisClient.Do(ctx, cmd).Error()
	} else {
		cmd := theRedisClient.B().Set().Key(key).Value(value).Build()
		return theRedisClient.Do(ctx, cmd).Error()
	}
}

// GetCache gets a value from Redis
func GetCache(key string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result := theRedisClient.Do(ctx, theRedisClient.B().Get().Key(key).Build())
	if result.Error() != nil {
		return "", result.Error()
	}

	return result.ToString()
}

// DeleteCache deletes a key from Redis
func DeleteCache(key string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	return theRedisClient.Do(ctx, theRedisClient.B().Del().Key(key).Build()).Error()
}

// ExistsCache checks if a key exists in Redis
func ExistsCache(key string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result := theRedisClient.Do(ctx, theRedisClient.B().Exists().Key(key).Build())
	if result.Error() != nil {
		return false, result.Error()
	}

	count, err := result.ToInt64()
	return count > 0, err
}
