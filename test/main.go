// package main
//
// import (
//
//	"encoding/json"
//	"log"
//	"qing-feeds/utils"
//	"time"
//
// )
//
//	type FeedLocation struct {
//		Key   string `json:"key"`
//		Value string `json:"value"`
//	}
//
//	func main() {
//		var location *FeedLocation
//		_ = json.Unmarshal([]byte("{\"key\":\"123\",\"value\":\"456\"}"), &location)
//		log.Printf("%+v", location) // &{Key:123 Value:456}
//
//		nano := time.Now().UnixNano()
//		id := utils.GenerateID()
//		log.Printf("id=%v nano=%v", id, nano) // id=78837196886384640 nano=1754457052462907000
//	}
package main

import (
	"fmt"
	"log"
	"math/rand"

	"github.com/sqids/sqids-go"
)

func main() {

	for i := 0; i < 10; i++ {
		randomCount := rand.Intn(10) + 1 // 随机 [1-10]
		log.Printf("random count: %d", randomCount)
	}
	//return

	//target := "974576566"
	//
	//// 输入字符集
	//digits := "123456789"
	//
	//// 结果存储
	//var results []string
	//
	//// 调用全排列生成函数
	//generatePermutations(digits, "", &results)
	//
	//// 打印结果
	//fmt.Printf("Total combinations: %d\n", len(results))
	//
	//for _, combination := range results {
	//	s, _ := sqids.New(sqids.Options{
	//		//Alphabet:  "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
	//		Alphabet:  combination,
	//		MinLength: 9,
	//	})
	//
	//	//id, err := s.Decode([]uint64{target}) // "pboC6MHhG4uIa3"
	//	numbers := s.Decode(target) // [78837196886384640]
	//	if len(numbers) == 1 {
	//		log.Printf("combination=%v numbers=%v", combination, numbers)
	//		time.Sleep(time.Second)
	//
	//	}
	//
	//	//if err != nil {
	//	//	log.Printf("combination=%v err=%v", combination, err)
	//	//} else {
	//	//	log.Printf("combination=%v id=%v", combination, id)
	//	//}
	//}

	s, _ := sqids.New(sqids.Options{
		//Alphabet:  "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
		Alphabet:  "123456789",
		MinLength: 11,
	})

	for i := 1000000000; i < 1000000000+10; i++ {
		id, _ := s.Encode([]uint64{uint64(i)}) // "pboC6MHhG4uIa3"
		numbers := s.Decode(id)                // [78837196886384640]
		fmt.Println(id, numbers)
	}

	// 超多1亿就会变成11位

}

// 递归生成全排列
func generatePermutations(chars string, current string, results *[]string) {
	// 如果所有字符都用完，则将当前排列加入结果集
	if len(chars) == 0 {
		*results = append(*results, current)
		return
	}

	// 遍历每个字符，依次固定当前字符并递归处理剩余字符
	for i, char := range chars {
		remaining := chars[:i] + chars[i+1:] // 剩余字符
		generatePermutations(remaining, current+string(char), results)
	}
}
