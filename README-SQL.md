
1,用户基本信息表 user_basic_profiles
```sql
CREATE TABLE user_basic_profiles (
    qid BIGINT
    phone varchar(20)
    custom_qid varchar(100)
    username varchar(100)
    avatar TEXT
    role REAL
    birthday BIGINT
    region json
    has_initialized bool 
);
```
2，用户凭证表 user_credentials
```sql
CREATE TABLE user_credentials (
    qid BIGINT
    crdt_type int
    crdt_key varchar(50)
    crdt_value varchar(500)
    created_at BIGINT
    expire_at BIGINT
    status int
    extra json
);


```

1, 动态表 (feeds)
```sql
CREATE TABLE feeds (
    feed_id BIGINT PRIMARY KEY,
    feed_qid BIGINT,
    content TEXT,
    vis_type INT, // 可见性类型 0:私密 1:公开 2:部分人可见 3:部分人不可见
    public BOOLEAN, // 是否是公开动态(同步到好友圈)
    location JSON,
    created_at BIGINT,
    update_at BIGINT,
    status INT // 0:未审核 1:机审通过 2:人审通过 -1:机审不通过 -2:人审不通过 -3:删除
    extra JSON // 扩展字段 
);
```

2, 动态分组表 (user_feed_groups)
```sql
CREATE TABLE user_feed_groups (
    group_id VARCHAR(50) PRIMARY KEY,
    group_name VARCHAR(50),
    owner_qid VARCHAR(50),
    group_type INT, // 分组类型 0:临时分组 1:固定分组
    created_at BIGINT,
    update_at BIGINT,
    status INT // 0:正常 -1:删除
);
```

3, 分组成员表 (user_feed_group_members)
```sql
CREATE TABLE user_feed_group_members (
    group_id VARCHAR(50),
    owner_qid VARCHAR(50),
    member_qid VARCHAR(50),
    created_at BIGINT
);
```

4, 动态可见性表 (feed_visibilities)
```sql
CREATE TABLE feed_visibilities (
    feed_id VARCHAR(50),
    group_id VARCHAR(50),
    vis_type INT, // 规则 0:私密 1:公开 2:白名单 3:黑名单
);
```

5，用户可见动态表 (user_visible_feeds)
```sql
CREATE TABLE user_visible_feeds (
    qid VARCHAR(50),
    feed_id VARCHAR(50),
    feed_qid VARCHAR(50),
    created_at BIGINT
);
```

6, 动态媒体表 (feed_medias)
```sql
CREATE TABLE feed_medias (
    media_id BIGINT PRIMARY KEY,
    feed_id BIGINT,
    feed_qid BIGINT,
    media_type INT, // 1:图片 2:视频
    media_order INT, // 顺序
    media_object_key VARCHAR(50), // 存储在s3的objectKey
    created_at BIGINT,
    thumbhash VARCHAR(50),
    file_size INT,
    duration INT // 视频/音频时长 单位毫秒
    width INT,
    height INT,
    exif JSONB, // exif信息
    extra JSONB // 扩展字段 
);
```

7, 动态评论表
```sql
CREATE TABLE feed_comments (
    comment_id BIGINT PRIMARY KEY,
    feed_id BIGINT,
    feed_qid BIGINT,
    qid BIGINT, // 评论者qid
    content TEXT,
    parent_id BIGINT NULL, // 父评论id，为空代表是一级评论，不为空代表是二级评论
    reply_to_comment_id BIGINT NULL, // 回复的【哪条】评论
    reply_to_qid BIGINT NULL, // 回复的【哪个人】的评论
    created_at BIGINT, 
    status INT // 0:正常 -1:删除
);
```

8，点赞表
```sql
CREATE TABLE feed_likes (
    feed_id VARCHAR(50),
    feed_qid VARCHAR(50), // 动态发布者qid
    qid VARCHAR(50),
    created_at BIGINT
);
```

9，用户关系表
```sql
CREATE TABLE user_relations (
    qid VARCHAR(50),
    target_qid VARCHAR(50),
    relationship_type INT, // 1:好友 2:拉黑
    created_at BIGINT
);
```

10, 待审资料审核表
```sql
CREATE TABLE pending_review_profiles (
    task_id BIGINT PRIMARY KEY, // 任务id
    qid VARCHAR(50),
    scenario INT, // 场景 1:修改qingID 2:修改头像 3:修改昵称 4:修改简介 5:修改个人主页封面 6:修改好友圈封面 等等
    content TEXT, // 待审核的资料内容
    created_at BIGINT,
    status INT, // 0:待审核 1:审核通过 -1:审核不通过 -2:用户已取消(比如新待审头像覆盖了旧待审头像)
    extra json
);
```

11, 待审动态表
```sql
CREATE TABLE pending_review_feeds (
    task_id BIGINT PRIMARY KEY, // 任务id
    feed_id VARCHAR(50),
    created_at BIGINT,
    status INT, // 0:待审核 1:审核通过 -1:审核不通过
);
```

12, 用户产生的媒体表
// 这个表的存在就是记录用户所有产生的媒体资源，因为用户上传资源后，可能并不会用，比如上传了头像，在保存前但又更换了头像，此时第一个头像是可访问的资源，但并没有被用户使用，存在内容安全问题（黄图或者泄漏隐私）
// 用这表，配合定时脚本，删除表中已经使用的资源，并把符合条件的未使用的资源删除，加强内容安全。
```sql
CREATE TABLE user_gen_medias (
    media_id int64,
    qid int64,
    scenario INT, // 场景 1:设置头像 2:发布动态
    file_size bigint,
    media_object_key TEXT,
    status INT, // -2:已删除 -1:未通过审核 0:默认待用 1:已使用 2:已审核 （如果用户上传了3个图，实际发布动态时只用了两个，那么另外一个图需要设置成-2）
    created_at BIGINT,
    extra JSON
);
```

修改动态时：会直接修改动态表里的内容，其次就是动态媒体表里的内容。那此时如果增加/删除了媒体，应该如何处理。
如果修改了媒体，新媒体会添加到feed_medias表中，旧的媒体则会被删除。此时就需要注意删除旧媒体关联的S4资源，可以再次插入到user_gen_medias中，然后设置状态是用户已删除。


13, 动态分发任务表
// 当发布动态后，需要把动态id扩散写入到user_visible_feeds中，这样才能让所有好友看到
// 这个表就是记录了所有的扩散写任务
```sql
CREATE TABLE feed_fanout_task (
    qid int64,
    feed_id int64,
    scenario INT, // 场景 1:发布动态 2:删除动态 3:修改动态可见范围 4: 好友关系变更 5:修改好友分组
    status INT, // 0: 待处理 1:处理中 2:处理成功 -1:处理失败
    created_at BIGINT,
    extra JSON
);
```

⚠️注意
```aiignore
如何清理用户已经上传，但实际没有用到的资源？因为这些资源可能涉及内容安全问题。黄图之类的。
比如：动态的图片和视频，上传后预保存到feed_medias表，只是尚未绑定feed_id。后续可以找那些未绑定feed_id的记录进行清理。
用户的头像：在待审核资料表，找出那些异常状态的记录进行清理。比如审核不通，用户已取消等状态
动态在审核不通过时，比如图片有问题或者文本有问题，需要删除对应的资源（无论资源是有问题还是没问题），这里涉及资源安全问题（一个审核不通过的动态，关联的资源却可访问）
```

