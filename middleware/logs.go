package middleware

import (
	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// RequestLogger middleware logs incoming requests
func RequestLogger() iris.Handler {
	return func(ctx iris.Context) {
		logrus.WithFields(logrus.Fields{
			"method":      ctx.Method(),
			"path":        ctx.Path(),
			"remote_addr": ctx.RemoteAddr(),
			"x-real-ip":   ctx.GetHeader("X-Real-IP"),
			"user_agent":  ctx.<PERSON>Header("User-Agent"),
			"device_id":   ctx.GetHeader("X-Device-Id"),
		}).Info("收到请求")

		ctx.Next()

		logrus.WithFields(logrus.Fields{
			"method":      ctx.Method(),
			"path":        ctx.Path(),
			"status_code": ctx.GetStatusCode(),
			"remote_addr": ctx.RemoteAddr(),
		}).Info("请求完成")
	}
}
