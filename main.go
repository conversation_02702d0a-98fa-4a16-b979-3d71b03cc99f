package main

import (
	"context"
	"os"
	"os/signal"
	"qing-feeds/config"
	"qing-feeds/controllers"
	"qing-feeds/database"
	"qing-feeds/middleware"
	"syscall"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/logger"
	"github.com/kataras/iris/v12/middleware/recover"
	"github.com/sirupsen/logrus"
)

func main() {
	// 获取配置
	cfg := config.GetConfig()

	// 设置日志
	setupLogging(cfg)

	// 初始化数据库
	if err := database.InitDatabases(); err != nil {
		logrus.WithError(err).Fatal("初始化数据库失败")
	}
	defer database.CloseDatabases()

	// 创建iris app
	app := iris.New()

	// 配置Iris
	setupIris(app)

	// 设置中间件
	setupMiddleware(app)

	// 设置路由
	setupRoutes(app)

	// 启动服务器
	startServer(app, cfg)
}

// 设置日志
func setupLogging(cfg *config.Config) {
	// 根据环境设置日志级别
	if cfg.IsProduction() {
		logrus.SetLevel(logrus.InfoLevel)
		logrus.SetFormatter(&logrus.JSONFormatter{})
	} else {
		logrus.SetLevel(logrus.DebugLevel)
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	logrus.WithField("env", cfg.Server.Env).Info("日志配置完成")
}

// 设置Iris
func setupIris(app *iris.Application) {
	// 配置Iris设置
	app.Configure(iris.WithConfiguration(iris.Configuration{
		DisableStartupLog:                 false,
		DisableInterruptHandler:           true,
		DisablePathCorrection:             false,
		EnablePathEscape:                  false,
		FireMethodNotAllowed:              true,
		DisableBodyConsumptionOnUnmarshal: false,
		TimeFormat:                        "2006-01-02 15:04:05",
		Charset:                           "UTF-8",
	}))
}

// 设置中间件
func setupMiddleware(app *iris.Application) {
	// 恢复中间件
	app.Use(recover.New())

	// 请求日志中间件
	app.Use(logger.New())

	// 自定义请求日志中间件
	app.Use(middleware.RequestLogger())
}

// 设置路由
func setupRoutes(app *iris.Application) {
	// 初始化控制器
	healthController := controllers.NewHealthController()
	profileController := controllers.NewProfileController()

	// 公共路由（无需头部验证）
	app.Get("/ping", healthController.Ping)

	// API路由（统一处理，中间件根据路径判断是否需要认证）
	api := app.Party("/api")
	api.Use(middleware.CheckRequestSignature()) // 请求签名中间件
	api.Use(middleware.CheckAuthorization())    // 必需的授权中间件
	api.Use(middleware.RequestHeaders())        // 请求头处理中间件
	{
		// 受保护的API端点（需要认证）
		api.Post("/users/profile/initialize", profileController.InitializeProfile) // 初始化用户资料
		api.Get("/users/profile/roles", profileController.GetRoles)                // 获取角色列表
	}
}

// 启动服务器
func startServer(app *iris.Application, cfg *config.Config) {
	// 服务器地址
	addr := ":" + cfg.Server.Port

	// 创建一个通道来监听中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 在goroutine中启动服务器
	go func() {
		logrus.WithFields(logrus.Fields{
			"port": cfg.Server.Port,
			"env":  cfg.Server.Env,
		}).Info("启动HTTP服务器")

		if err := app.Listen(addr); err != nil {
			logrus.WithError(err).Error("服务器启动失败")
		}
	}()

	// 等待中断信号
	<-quit
	logrus.Info("正在关闭服务器...")

	// 创建带超时的上下文用于优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭服务器
	if err := app.Shutdown(ctx); err != nil {
		logrus.WithError(err).Error("服务器强制关闭")
	} else {
		logrus.Info("服务器优雅停止")
	}
}
