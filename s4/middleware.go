package s4

import (
	"bytes"
	"context"
	"fmt"
	"github.com/aws/smithy-go"
	"io"

	"github.com/aws/smithy-go/middleware"
	smithyhttp "github.com/aws/smithy-go/transport/http"
)

const userAgentValue = "bitiful/security/nicol0802"
const userAgentKey = "User-Agent"

var presignedUrlAddParamsMiddleware = middleware.BuildMiddlewareFunc("Bitiful:PresignedUrlAddParams", func(ctx context.Context, input middleware.BuildInput, next middleware.BuildHandler) (out middleware.BuildOutput, metadata middleware.Metadata, err error) {
	bitifulAdditionalParams := ctx.Value("bitiful-additional-params")
	if bitifulAdditionalParams == nil {
		return next.HandleBuild(ctx, input)
	}

	req, ok := input.Request.(*smithyhttp.Request)
	if !ok {
		return out, metadata, fmt.Errorf("unknown transport type %T", req)
	}
	// set image scale params

	bitifulAdditionalParamsMap, ok := bitifulAdditionalParams.(map[string]string)
	if !ok {
		return next.HandleBuild(ctx, input)
	}

	query := req.URL.Query()
	for key, value := range bitifulAdditionalParamsMap {
		query.Set(key, value)
	}
	req.URL.RawQuery = query.Encode()

	return next.HandleBuild(ctx, input)
})

// 获取预签名url增加自定义参数
func RegisterPresignedUrlAddParamsMiddleware(stack *middleware.Stack) error {
	// Attach the custom middleware to the beginning of the Initialize step
	return stack.Build.Add(presignedUrlAddParamsMiddleware, middleware.After)
}

type copyResponseRawBodyKey struct{}

func GetResponseRawBody(metadata middleware.Metadata) ([]byte, error) {
	customMetadata, ok := metadata.Get(copyResponseRawBodyKey{}).([]byte)
	if !ok || customMetadata == nil {
		return nil, fmt.Errorf("can not get customMetadata")
	}
	return customMetadata, nil
}

func setResponseRawBody(metadata *middleware.Metadata, customMetadata []byte) {
	metadata.Set(copyResponseRawBodyKey{}, customMetadata)
}

var copyResponseRawBodyMiddleware = middleware.DeserializeMiddlewareFunc("Bitiful:CopyResponseRawBody", func(ctx context.Context, input middleware.DeserializeInput, next middleware.DeserializeHandler) (out middleware.DeserializeOutput, metadata middleware.Metadata, err error) {
	out, metadata, err = next.HandleDeserialize(ctx, input)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)

	if !ok {
		return out, metadata, nil
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, nil
	}

	data, err := io.ReadAll(response.Body)
	if err != nil {
		return out, metadata, &smithy.DeserializationError{Err: err}
	}

	// Consume the full body to prevent TCP connection resets on some platforms
	_, _ = io.Copy(io.Discard, response.Body)
	// Do not validate that the response closes successfully.
	_ = response.Body.Close()

	setResponseRawBody(&metadata, data)

	response.Body = io.NopCloser(bytes.NewBuffer(data))
	return out, metadata, nil
})

func RegisterCopyResponseRawBodyMiddleware(stack *middleware.Stack) error {
	return stack.Deserialize.Add(copyResponseRawBodyMiddleware, middleware.After)
}

var customUserAgentMiddleware = middleware.BuildMiddlewareFunc("Bitiful:CustomUserAgent", func(
	ctx context.Context, input middleware.BuildInput, next middleware.BuildHandler,
) (
	out middleware.BuildOutput, metadata middleware.Metadata, err error,
) {
	request, ok := input.Request.(*smithyhttp.Request)
	if !ok {
		return out, metadata, fmt.Errorf("unknown transport type %T", input.Request)
	}

	value := request.Header.Get(userAgentKey)

	if len(value) > 0 {
		value = value + " " + userAgentValue
	} else {
		value = userAgentValue
	}

	request.Header.Set(userAgentKey, value)

	return next.HandleBuild(ctx, input)
})

func RegisterCustomUserAgentMiddleware(stack *middleware.Stack) error {
	return stack.Build.Add(customUserAgentMiddleware, middleware.After)
}
