package s4

import (
	"context"
	"qing-feeds/config"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/aws/aws-sdk-go-v2/aws"
	s3Config "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

const DefaultEndPoint = "s3.bitiful.net"

const DefaultRegion = "cn-east-1"

var theS3Client *s3.Client
var theBucket = ""

func init() {
	cfg := config.GetConfig()
	s3client, err := getS3Client(cfg.S4.AK, cfg.S4.SK)
	if err != nil {
		logrus.WithError(err).Fatal("创建s4 client失败")
		return
	}
	theS3Client = s3client
	theBucket = cfg.S4.Bucket
}

func GetUploadUrl(objectKey string, dataSize int64) string {
	presignedUrl := getPreSignedPutUrl(theS3Client, theBucket, objectKey, dataSize)
	return presignedUrl
}

func getPreSignedPutUrl(s3client *s3.Client, bucket string, objectKey string, objectSize int64) string {
	ctx := context.TODO()
	preSignClient := s3.NewPresignClient(s3client)

	// 	常规无参数的
	preSignedRequest, _ := preSignClient.PresignPutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(objectKey),
		//ContentLength: aws.Int64(objectSize),
	}, func(presignOptions *s3.PresignOptions) {
		presignOptions.Expires = time.Hour // 默认是900秒，改为1小时
	})

	return preSignedRequest.URL
}

func getS3Client(key, secret string) (*s3.Client, error) {
	customProvider := credentials.NewStaticCredentialsProvider(key, secret, "")
	cfg, err := s3Config.LoadDefaultConfig(context.TODO(),
		s3Config.WithCredentialsProvider(customProvider),
		s3Config.WithRegion(DefaultRegion))
	if err != nil {
		return nil, err
	}

	// 创建S3客户端并配置自定义端点
	s3client := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.BaseEndpoint = aws.String("https://" + DefaultEndPoint)
	})
	return s3client, nil
}
