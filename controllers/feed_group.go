package controllers

import (
	"qing-feeds/models"
	"qing-feeds/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// FeedGroupController 处理用户组相关的端点
type FeedGroupController struct{}

// NewFeedGroupController 创建新的用户组控制器
func NewFeedGroupController() *FeedGroupController {
	return &FeedGroupController{}
}

// CreateFeedGroupRequest 创建用户组请求
type CreateFeedGroupRequest struct {
	GroupName string   `json:"group_name" validate:"required"`
	GroupType int      `json:"group_type" validate:"required,min=0,max=1"`
	Members   []string `json:"members,omitempty"`
}

// FeedGroupResponse 用户组响应
type FeedGroupResponse struct {
	GroupID     int64    `json:"group_id"`
	GroupName   string   `json:"group_name"`
	GroupType   int      `json:"group_type"`
	MemberCount int      `json:"member_count"`
	Members     []string `json:"members,omitempty"`
	CreatedAt   int64    `json:"created_at"`
	UpdateAt    int64    `json:"update_at"`
}

// CreateFeedGroup 处理 POST /api/feed-groups
// 创建用户组
func (fg *FeedGroupController) CreateFeedGroup(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req CreateFeedGroupRequest
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("CreateFeedGroup 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 验证请求参数
	if err := fg.validateCreateFeedGroupRequest(&req); err != nil {
		utils.InvalidRequestError(ctx, err.Error())
		return
	}

	// 生成分组ID
	groupID := utils.GenerateFeedGroupID()

	now := time.Now().UnixMilli()

	// 创建用户组
	group := &models.FeedGroupDBItem{
		GroupID:   groupID,
		GroupName: req.GroupName,
		OwnerQid:  qid,
		GroupType: req.GroupType,
		CreatedAt: now,
		UpdateAt:  now,
		Status:    0, // 正常状态
	}

	if err := fg.insertFeedGroup(group); err != nil {
		logrus.WithError(err).Error("创建用户组失败")
		utils.InternalError(ctx, "")
		return
	}

	// 添加成员
	if len(req.Members) > 0 {
		if err := fg.addMembersToGroup(groupID, qid, req.Members, now); err != nil {
			logrus.WithError(err).Error("添加用户组成员失败")
			utils.InternalError(ctx, "")
			return
		}
	}

	// 构建响应
	response := &FeedGroupResponse{
		GroupID:     groupID,
		GroupName:   req.GroupName,
		GroupType:   req.GroupType,
		MemberCount: len(req.Members),
		Members:     req.Members,
		CreatedAt:   now,
		UpdateAt:    now,
	}

	logrus.WithFields(logrus.Fields{
		"group_id":   groupID,
		"group_name": req.GroupName,
		"owner_qid":  qid,
	}).Info("用户组创建成功")

	utils.SuccessWithMsg(ctx, response, "")
}

// DeleteFeedGroup 处理 DELETE /api/feed-groups/:group_id
// 删除用户组
func (fg *FeedGroupController) DeleteFeedGroup(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	groupIDStr := strings.TrimSpace(ctx.Params().Get("group_id"))
	if groupIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"group_id": groupIDStr,
			"qid":      qid,
		}).Error("DeleteFeedGroup 无效的分组ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码ID
	groupID, err := utils.DecodeFeedGroupID(groupIDStr)
	if err != nil || groupID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"group_id": groupID,
			"qid":      qid,
		}).Error("CreateMomentsFeedComment 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 验证分组是否存在且属于当前用户
	group, err := fg.getFeedGroupByID(groupID)
	if err != nil {
		logrus.WithError(err).Error("查询用户组失败")
		utils.InternalError(ctx, "")
		return
	}

	if group == nil {
		utils.InvalidRequestError(ctx, "用户组不存在")
		return
	}

	if group.OwnerQid != qid {
		utils.InvalidRequestError(ctx, "无权限删除此用户组")
		return
	}

	if group.Status == 1 { // 已删除
		utils.InvalidRequestError(ctx, "用户组已删除")
		return
	}

	// 软删除用户组
	if err := fg.softDeleteFeedGroup(groupID); err != nil {
		logrus.WithError(err).Error("删除用户组失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"group_id":  groupID,
		"owner_qid": qid,
	}).Info("用户组删除成功")

	utils.SuccessWithMsg(ctx, nil, "")
}

// UpdateFeedGroupRequest 修改用户组请求
type UpdateFeedGroupRequest struct {
	GroupName string `json:"group_name" validate:"required"`
}

// UpdateFeedGroup 处理 PUT /api/feed-groups/:group_id
// 修改用户组名称
func (fg *FeedGroupController) UpdateFeedGroup(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	groupIDStr := strings.TrimSpace(ctx.Params().Get("group_id"))
	if groupIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"group_id": groupIDStr,
			"qid":      qid,
		}).Error("UpdateFeedGroup 无效的分组ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码ID
	groupID, err := utils.DecodeFeedGroupID(groupIDStr)
	if err != nil || groupID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"group_id": groupID,
			"qid":      qid,
		}).Error("UpdateFeedGroup 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	var req UpdateFeedGroupRequest
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateFeedGroup 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 验证请求参数
	if strings.TrimSpace(req.GroupName) == "" {
		utils.InvalidRequestError(ctx, "分组名称不能为空")
		return
	}

	// 验证分组是否存在且属于当前用户
	group, err := fg.getFeedGroupByID(groupID)
	if err != nil {
		logrus.WithError(err).Error("查询用户组失败")
		utils.InternalError(ctx, "")
		return
	}

	if group == nil {
		utils.InvalidRequestError(ctx, "用户组不存在")
		return
	}

	if group.OwnerQid != qid {
		utils.InvalidRequestError(ctx, "无权限修改此用户组")
		return
	}

	if group.Status == 1 { // 已删除
		utils.InvalidRequestError(ctx, "用户组已删除")
		return
	}

	// 更新用户组名称
	if err := fg.updateFeedGroupName(groupID, req.GroupName); err != nil {
		logrus.WithError(err).Error("更新用户组名称失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"group_id":   groupID,
		"group_name": req.GroupName,
		"owner_qid":  qid,
	}).Info("用户组名称修改成功")

	utils.SuccessWithMsg(ctx, nil, "")
}

// AddMembersRequest 添加成员请求
type AddMembersRequest struct {
	Members []string `json:"members" validate:"required"`
}

// AddMembers 处理 POST /api/feed-groups/:group_id/members
// 添加用户到用户组
func (fg *FeedGroupController) AddMembers(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	groupIDStr := strings.TrimSpace(ctx.Params().Get("group_id"))
	if groupIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"group_id": groupIDStr,
			"qid":      qid,
		}).Error("UpdateFeedGroup 无效的分组ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码ID
	groupID, err := utils.DecodeFeedGroupID(groupIDStr)
	if err != nil || groupID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"group_id": groupID,
			"qid":      qid,
		}).Error("UpdateFeedGroup 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	var req AddMembersRequest
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("AddMembers 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 验证请求参数
	if len(req.Members) == 0 {
		utils.InvalidRequestError(ctx, "成员列表不能为空")
		return
	}

	// 验证分组是否存在且属于当前用户
	group, err := fg.getFeedGroupByID(groupID)
	if err != nil {
		logrus.WithError(err).Error("查询用户组失败")
		utils.InternalError(ctx, "")
		return
	}

	if group == nil {
		utils.InvalidRequestError(ctx, "用户组不存在")
		return
	}

	if group.OwnerQid != qid {
		utils.InvalidRequestError(ctx, "无权限修改此用户组")
		return
	}

	if group.Status == 1 { // 已删除
		utils.InvalidRequestError(ctx, "用户组已删除")
		return
	}

	// 添加成员
	now := time.Now().UnixMilli()
	if err := fg.addMembersToGroup(groupID, qid, req.Members, now); err != nil {
		logrus.WithError(err).Error("添加用户组成员失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"group_id":     groupID,
		"owner_qid":    qid,
		"member_count": len(req.Members),
	}).Info("用户组成员添加成功")

	utils.SuccessWithMsg(ctx, nil, "")
}

// RemoveMembersRequest 移除成员请求
type RemoveMembersRequest struct {
	Members []string `json:"members" validate:"required"`
}

// RemoveMembers 处理 DELETE /api/feed-groups/:group_id/members
// 从用户组中移除用户
func (fg *FeedGroupController) RemoveMembers(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	groupIDStr := strings.TrimSpace(ctx.Params().Get("group_id"))
	if groupIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"group_id": groupIDStr,
			"qid":      qid,
		}).Error("UpdateFeedGroup 无效的分组ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码ID
	groupID, err := utils.DecodeFeedGroupID(groupIDStr)
	if err != nil || groupID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"group_id": groupID,
			"qid":      qid,
		}).Error("UpdateFeedGroup 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	var req RemoveMembersRequest
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("RemoveMembers 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 验证请求参数
	if len(req.Members) == 0 {
		utils.InvalidRequestError(ctx, "成员列表不能为空")
		return
	}

	// 验证分组是否存在且属于当前用户
	group, err := fg.getFeedGroupByID(groupID)
	if err != nil {
		logrus.WithError(err).Error("查询用户组失败")
		utils.InternalError(ctx, "")
		return
	}

	if group == nil {
		utils.InvalidRequestError(ctx, "用户组不存在")
		return
	}

	if group.OwnerQid != qid {
		utils.InvalidRequestError(ctx, "无权限修改此用户组")
		return
	}

	if group.Status == 1 { // 已删除
		utils.InvalidRequestError(ctx, "用户组已删除")
		return
	}

	// 移除成员
	if err := fg.removeMembersFromGroup(groupID, qid, req.Members); err != nil {
		logrus.WithError(err).Error("移除用户组成员失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"group_id":     groupID,
		"owner_qid":    qid,
		"member_count": len(req.Members),
	}).Info("用户组成员移除成功")

	utils.SuccessWithMsg(ctx, nil, "")
}

// FeedGroupListResponse 用户组列表响应
type FeedGroupListResponse struct {
	List []FeedGroupResponse `json:"list"`
}

// GetFeedGroups 处理 GET /api/feed-groups
// 获取用户组列表
func (fg *FeedGroupController) GetFeedGroups(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	// 查询用户组列表
	groups, err := fg.getFeedGroupsByOwner(qid)
	if err != nil {
		logrus.WithError(err).Error("查询用户组列表失败")
		utils.InternalError(ctx, "")
		return
	}

	response := &FeedGroupListResponse{
		List: groups,
	}

	utils.SuccessWithMsg(ctx, response, "")
}
