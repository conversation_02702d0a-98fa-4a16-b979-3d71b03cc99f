package controllers

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"qing-feeds/database"
	"qing-feeds/models"
	"qing-feeds/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// CreateFeedRequest 发布动态请求
type CreateFeedRequest struct {
	Content  string                   `json:"content" validate:"required"`
	VisType  string                   `json:"vis_type" validate:"required"`
	Public   bool                     `json:"public,omitempty"`
	GroupIDs []string                 `json:"group_ids,omitempty"`
	Qids     []string                 `json:"qids,omitempty"`
	Location *FeedLocation            `json:"location,omitempty"`
	Images   []CreateFeedRequestImage `json:"images,omitempty"`
	Videos   []CreateFeedRequestVideo `json:"videos,omitempty"`
}
type CreateFeedRequestImage struct {
	MediaId string `json:"media_id"`
	Width   int    `json:"width"`
	Height  int    `json:"height"`
}

type CreateFeedRequestVideo struct {
	MediaId  string `json:"media_id"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	Duration int    `json:"duration"`
}

func (req CreateFeedRequest) locationJsonValue() any {
	var location any = nil
	if req.Location != nil {
		locationBytes, marshalErr := json.Marshal(req.Location)
		if marshalErr == nil {
			locationJson := string(locationBytes)
			if locationJson != "" {
				location = locationJson
			}
		}
	}
	return location
}

type CreateFeedResponse struct {
	FeedId string `json:"feed_id"`
}

// CreateFeed 处理 POST /api/feeds
// 发布动态
func (f *FeedController) CreateFeed(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	var req CreateFeedRequest
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("CreateFeed 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 验证请求参数
	if err := f.validateCreateFeedRequest(&req); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("CreateFeed validateCreateFeedRequest 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 生成动态ID
	feedID := utils.GenerateFeedID()

	// 处理媒体数据 用户已经上传了的媒体，在数据库中的字段并不全，需要补充完整，比如缩略图，thumbhash，exif等信息等，这里做到尽力而为就好，能获取到的就补进去
	err := f.prepareMedias(feedID, qid, req.Images, req.Videos)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"feed_id": feedID,
		}).Error("prepareMedias 失败")
		utils.InternalError(ctx, "")
		return
	}

	// 保存动态
	code, err := f.saveNewFeed(qid, feedID, &req)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"feed_id": feedID,
		}).Error("CreateFeed saveNewFeed 失败")

		if code == utils.CodeInvalidRequest {
			utils.InvalidRequestError(ctx, err.Error())
			return
		}

		utils.InternalError(ctx, "")
		return
	}

	// TODO 找出那些已经上传，但是没有使用的资源移除，比如上传了一张图，但是后来又删除了。此时这个资源就是可访问的【垃圾文件】

	// 写入动态分发任务表
	saveErr := f.saveFanoutTask(feedID, qid, 1) // 场景 1:发布动态 2:删除动态 3:修改动态可见范围 4:好友关系变更
	if saveErr != nil {
		// 这是一个重量级非常高的错误，需要发出通知及时处理
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"feed_id": feedID,
		}).Error("CreateFeed saveFanoutTask 失败")
	}

	logrus.WithFields(logrus.Fields{
		"qid":      qid,
		"feed_id":  feedID,
		"feed_qid": qid,
		"vis_type": req.VisType,
	}).Info("动态发布成功")

	feedIDStr, _ := utils.EncodeFeedID(feedID)
	response := &CreateFeedResponse{
		FeedId: feedIDStr,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

func (f *FeedController) saveFanoutTask(feedID, qid int64, scenario int) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	now := time.Now().UnixMilli()
	status := 0 // 0: 待处理 1:处理成功 -1:处理失败
	insertFeedQuery := `
		INSERT INTO feed_fanout_task (qid, feed_id, scenario, status, created_at, extra)
		VALUES ($1, $2, $3, $4, $5, $6)
	`
	_, err := db.Exec(insertFeedQuery,
		qid,
		feedID,
		scenario,
		status,
		now,
		nil,
	)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":      qid,
			"feed_id":  feedID,
			"scenario": scenario,
		}).Error("saveFanoutTask 插入动态分发任务表失败")
		return err
	}
	return nil
}

// 准备媒体数据 补充动态媒体表
func (f *FeedController) prepareMedias(feedID, qid int64, imageList []CreateFeedRequestImage, videoList []CreateFeedRequestVideo) error {
	if len(imageList) <= 0 && len(videoList) <= 0 {
		return nil
	}

	// 遍历 imageList和videoList，从s4获取额外信息，填充到数据库中
	pendingMediaMap, err := f.getPendingFeedMediaInfo(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"feed_id": feedID,
		}).Error("prepareMedias GetFeedUploadCache 失败")
		return err
	}

	// imageList和videoList中的媒体必须都存在
	for _, image := range imageList {
		mediaId, _ := utils.DecodeMediaID(image.MediaId)
		if _, exist := pendingMediaMap[mediaId]; exist == false {
			logrus.WithFields(logrus.Fields{
				"qid":          qid,
				"feed_id":      feedID,
				"media_id_str": image.MediaId,
				"media_id":     mediaId,
			}).Error("prepareMedias 找不到对应资源信息")
			return errors.New("找不到对应资源")
		}
	}

	for _, video := range videoList {
		mediaId, _ := utils.DecodeMediaID(video.MediaId)
		if _, exist := pendingMediaMap[mediaId]; exist == false {
			logrus.WithFields(logrus.Fields{
				"qid":          qid,
				"feed_id":      feedID,
				"video_id_str": video.MediaId,
				"media_id":     mediaId,
			}).Error("prepareMedias 找不到对应资源信息")
			return errors.New("找不到对应资源")
		}
	}

	// TODO 从s4获取额外信息，填充缩略图，thumbhash，exif等信息

	db := database.GetDB()
	// 开始数据库事务
	tx, err := db.Begin()
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"feed_id": feedID,
		}).Error("prepareMedias 开始事务失败")
		return err
	}

	// 更新动态媒体表 feed_medias 此时媒体已经插入到feed_medias中，只是没有关联的feed_id,此时做一下关联
	now := time.Now().UnixMilli()
	imageListLength := len(imageList)
	if imageListLength > 0 {
		if imageListLength == 1 {
			mediaDetail := imageList[0]
			mediaId, _ := utils.DecodeMediaID(mediaDetail.MediaId)
			mediaCache := pendingMediaMap[mediaId]
			mediaType := 1 // 1 image 2 video
			query := `INSERT INTO feed_medias (media_id, feed_id, feed_qid, media_type, media_order, media_object_key, created_at, thumbhash, file_size, duration, width, height, exif, extra) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) ON CONFLICT (media_id) DO NOTHING`
			_, err = tx.Exec(query, mediaId, feedID, qid, mediaType, 0, mediaCache.MediaObjectKey, now, "", mediaCache.FileSize, 0, mediaDetail.Width, mediaDetail.Height, nil, nil)
			if err != nil {
				_ = tx.Rollback()
				logrus.WithError(err).WithFields(logrus.Fields{
					"qid":     qid,
					"feed_id": feedID,
				}).Error("prepareMedias 更新单个image媒体失败")
				return err
			}
		} else {
			sqlStr := `INSERT INTO feed_medias (media_id, feed_id, feed_qid, media_type, media_order, media_object_key, created_at, thumbhash, file_size, duration, width, height, exif, extra) VALUES `
			valueStrings := make([]string, 0, imageListLength)
			valueArgs := make([]interface{}, 0, imageListLength*14) // 14个字段

			paramIndex := 1
			for index, mediaDetail := range imageList {
				mediaId, _ := utils.DecodeMediaID(mediaDetail.MediaId)
				mediaCache := pendingMediaMap[mediaId]
				mediaType := 1 // 1 image 2 video

				// 为每一行数据创建一个占位符组，例如 "($1, $2, $3, $4)"
				placeholders := fmt.Sprintf("($%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d, $%d)", paramIndex, paramIndex+1, paramIndex+2, paramIndex+3, paramIndex+4, paramIndex+5, paramIndex+6, paramIndex+7, paramIndex+8, paramIndex+9, paramIndex+10, paramIndex+11, paramIndex+12, paramIndex+13)
				valueStrings = append(valueStrings, placeholders)

				// 将这行数据的所有字段值按顺序添加到总的参数列表中
				valueArgs = append(valueArgs, mediaId)
				valueArgs = append(valueArgs, feedID)
				valueArgs = append(valueArgs, qid)
				valueArgs = append(valueArgs, mediaType)
				valueArgs = append(valueArgs, index)
				valueArgs = append(valueArgs, mediaCache.MediaObjectKey)
				valueArgs = append(valueArgs, now)
				valueArgs = append(valueArgs, "")
				valueArgs = append(valueArgs, mediaCache.FileSize)
				valueArgs = append(valueArgs, 0)
				valueArgs = append(valueArgs, mediaDetail.Width)
				valueArgs = append(valueArgs, mediaDetail.Height)
				valueArgs = append(valueArgs, nil)
				valueArgs = append(valueArgs, nil)

				// 更新下一个占位符的起始索引
				paramIndex += 14
			}

			// 拼接最终的SQL语句 例如: INSERT INTO ... VALUES ($1, ..., $4), ($5, ..., $8), ...
			finalSQL := sqlStr + strings.Join(valueStrings, ",") + " ON CONFLICT (media_id) DO NOTHING"
			_, err = db.Exec(finalSQL, valueArgs...)
			if err != nil {
				_ = tx.Rollback()
				logrus.WithError(err).WithFields(logrus.Fields{
					"qid":     qid,
					"feed_id": feedID,
				}).Error("prepareMedias 更新多个image媒体失败")
				return err
			}
		}
	}

	videoListLength := len(videoList)
	if videoListLength >= 1 {
		mediaDetail := videoList[0]
		mediaId, _ := utils.DecodeMediaID(mediaDetail.MediaId)
		mediaCache := pendingMediaMap[mediaId]
		mediaType := 2 // 1 image 2 video
		query := `INSERT INTO feed_medias (media_id, feed_id, feed_qid, media_type, media_order, media_object_key, created_at, thumbhash, file_size, duration, width, height, exif, extra) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) ON CONFLICT (media_id) DO NOTHING`
		_, err = tx.Exec(query, mediaId, feedID, qid, mediaType, 0, mediaCache.MediaObjectKey, now, "", mediaCache.FileSize, mediaDetail.Duration, mediaDetail.Width, mediaDetail.Height, nil, nil)
		if err != nil {
			_ = tx.Rollback()
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":     qid,
				"feed_id": feedID,
			}).Error("prepareMedias 更新video媒体失败")
			return err
		}
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"feed_id": feedID,
		}).Error("prepareMedias 提交事务失败")
		return err
	}

	return nil
}

// validateCreateFeedRequest 验证创建动态请求
func (f *FeedController) validateCreateFeedRequest(req *CreateFeedRequest) error {
	// 验证必填字段
	req.Content = strings.TrimSpace(req.Content)
	if req.Public && req.Content == "" {
		return fmt.Errorf("公域动态内容不能为空")
	}

	if req.VisType != "all" && req.VisType != "self" && req.VisType != "whitelist" && req.VisType != "blacklist" {
		return fmt.Errorf("无效的可见性类型")
	}

	// 如果是部分人可见或不可见，必须指定分组
	if (req.VisType == "whitelist" || req.VisType == "blacklist") && (len(req.GroupIDs) == 0 && len(req.Qids) == 0) {
		return fmt.Errorf("部分人可见或不可见时必须指定用户组")
	}

	// TODO Content需要鉴黄
	// TODO 验证MediaList中的资源是否有效，从数据库中查找
	// TODO 验证GroupIDs是否存在，且属于当前用户
	// TODO 验证Qids是否是好友，并去重

	return nil
}

// 分级配置影响可见范围 https://yvu7rxmi0tw.feishu.cn/wiki/UxsTw0jAUivhhnky0zIcbEQSnbb?from=from_copylink
// insertNewFeed 插入新动态 当返回的错误不是CodeInternalError时，会提示error.message，所以需要注意错误文本信息
func (f *FeedController) saveNewFeed(qid, feedID int64, req *CreateFeedRequest) (int, error) {
	// 1. 先插入数据库一个初始状态的动态
	now := time.Now().UnixMilli()
	db := database.GetDB()
	if db == nil {
		return utils.CodeInternalError, sql.ErrConnDone
	}

	{
		location := req.locationJsonValue()
		visType := f.stringVisTypeToInt(req.VisType)

		insertFeedQuery := `
		INSERT INTO feeds (feed_id, feed_qid, content, vis_type, public, location, created_at, update_at, status, extra)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`
		_, err := db.Exec(insertFeedQuery,
			feedID,
			qid,
			req.Content,
			visType,
			req.Public,
			location,
			now,
			0,
			FeedStatusDefault,
			nil,
		)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":     qid,
				"feed_id": feedID,
			}).Error("insertNewFeed 插入动态失败")
			return utils.CodeInternalError, err
		}
	}

	// 2. 然后再机器审核
	{
		auditResult, err := f.auditFeed(req)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":     qid,
				"feed_id": feedID,
			}).Error("insertNewFeed 机器审核执行失败")
			return utils.CodeInternalError, err
		}

		// 审核不通过
		if auditResult.OK == false {
			logrus.WithFields(logrus.Fields{
				"qid":     qid,
				"feed_id": feedID,
				"reason":  auditResult.Reason,
			}).Error("insertNewFeed 机器审核不通过")

			// 更新动态状态
			updateQuery := `UPDATE feeds SET status = $1 WHERE feed_id = $2`
			_, err = db.Exec(updateQuery, FeedStatusMachineRejected, feedID)
			if err != nil {
				logrus.WithError(err).WithFields(logrus.Fields{
					"qid":     qid,
					"feed_id": feedID,
					"status":  FeedStatusMachineRejected,
				}).Error("insertNewFeed 更新动态状态失败")
				//return err 这里不需要返回这个错误
			}
			return utils.CodeInvalidRequest, errors.New("当前内容可能包含不当信息，请修改内容后重试")
		}
	}

	// 3. 机器审核通过后，再修改状态，同时插入动态可见性表、动态媒体表、用户分组表、分组成员表等相关表
	{
		// 事务开始前，把能提前处理的都处理下
		pendingReviewTaskId := utils.GeneratePendingReviewTaskID()

		visGroups := make([]int64, 0)
		if len(req.GroupIDs) > 0 {
			for _, gidStr := range req.GroupIDs {
				if gid, _ := utils.DecodeFeedID(gidStr); gid > 0 {
					visGroups = append(visGroups, gid)
				}
			}
		}

		tmpGroupId := int64(0)
		if len(req.Qids) > 0 {
			tmpGroupId = utils.GenerateFeedGroupID()
			visGroups = append(visGroups, tmpGroupId)
		}

		// 开始数据库事务
		tx, err := db.Begin()
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":     qid,
				"feed_id": feedID,
			}).Error("insertNewFeed 开始事务失败")
			return utils.CodeInternalError, err
		}

		// 1. 更新动态状态为机器审核通过
		{
			updateQuery := `UPDATE feeds SET status = $1 WHERE feed_id = $2`
			_, err = tx.Exec(updateQuery, FeedStatusMachineApproved, feedID)
			if err != nil {
				logrus.WithError(err).WithFields(logrus.Fields{
					"qid":     qid,
					"feed_id": feedID,
					"status":  FeedStatusMachineApproved,
				}).Error("insertNewFeed 更新动态状态失败")

				_ = tx.Rollback()
				return utils.CodeInternalError, err
			}
		}

		// 2. 如果发布动态时，选了单独的几个好友 可见/不可见，此时需要创建一个临时组把这些好友加入到这个组中
		if len(req.Qids) > 0 && tmpGroupId > 0 {
			query := `INSERT INTO user_feed_groups (group_id, owner_qid, group_name, group_type, created_at, update_at, status) VALUES ($1, $2, $3, $4, $5, $6, $7)`
			_, err = tx.Exec(query, tmpGroupId, qid, "", models.GroupTypeTemporary, now, 0, models.GroupStatusNormal)
			if err != nil {
				_ = tx.Rollback()
				return utils.CodeInternalError, err
			}

			// 使用批量插入的方式插入分组成员表，为防止条数过多，每条语句最多插入1000条记录
			membersSlice := utils.SplitSlice(req.Qids, 1000)
			for _, members := range membersSlice {
				sqlStr := `INSERT INTO user_feed_group_members (group_id, owner_qid, member_qid, created_at) VALUES `

				valueStrings := make([]string, 0, len(members))
				valueArgs := make([]interface{}, 0, len(members)*4) // 4个字段

				paramIndex := 1
				for _, memberQid := range members {
					// 为每一行数据创建一个占位符组，例如 "($1, $2, $3, $4)"
					placeholders := fmt.Sprintf("($%d, $%d, $%d, $%d)", paramIndex, paramIndex+1, paramIndex+2, paramIndex+3)
					valueStrings = append(valueStrings, placeholders)

					// 将这行数据的所有字段值按顺序添加到总的参数列表中
					valueArgs = append(valueArgs, tmpGroupId)
					valueArgs = append(valueArgs, qid)
					valueArgs = append(valueArgs, memberQid)
					valueArgs = append(valueArgs, now)

					// 更新下一个占位符的起始索引
					paramIndex += 4
				}

				// 拼接最终的SQL语句 例如: INSERT INTO ... VALUES ($1, ..., $4), ($5, ..., $8), ...
				finalSQL := sqlStr + strings.Join(valueStrings, ",")
				_, err = db.Exec(finalSQL, valueArgs...)
				if err != nil {
					_ = tx.Rollback()
					return utils.CodeInternalError, err
				}
			}
		}

		// 3. 插入动态可见性表
		{
			for _, groupId := range visGroups {
				insertQuery := `INSERT INTO feed_visibilities (feed_id, group_id, vis_type, created_at) VALUES ($1, $2, $3, $4)`
				visType := f.stringVisTypeToInt(req.VisType)
				_, err = tx.Exec(insertQuery, feedID, groupId, visType, now)
				if err != nil {
					_ = tx.Rollback()
					return utils.CodeInternalError, err
				}
			}
		}

		// 4. 插入审核动态表
		{
			insertQuery := `INSERT INTO pending_review_feeds (task_id, feed_id, created_at, status) VALUES ($1, $2, $3, $4)`
			_, err = tx.Exec(insertQuery, pendingReviewTaskId, feedID, now, 0)
			if err != nil {
				_ = tx.Rollback()
				return utils.CodeInternalError, err
			}
		}

		// 提交事务
		if err = tx.Commit(); err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":     qid,
				"feed_id": feedID,
			}).Error("insertNewFeed 提交事务失败")
			return utils.CodeInternalError, err
		}
	}

	return utils.CodeSuccess, nil
}

//func (f *FeedController) GetFeedUploadCache(qid int64) (map[string]FeedPendingMedia, error) {
//	key := fmt.Sprintf("feed:media:upload:%v", qid)
//	medias, err := database.ZGetAllCache(key)
//	if err != nil {
//		return nil, err
//	}
//
//	mediaMap := map[string]FeedPendingMedia{}
//	for _, media := range medias {
//		mediaUploadCache := FeedPendingMedia{}
//		if uErr := json.Unmarshal([]byte(media), &mediaUploadCache); uErr == nil {
//			mediaMap[mediaUploadCache.MediaId] = mediaUploadCache
//		}
//	}
//
//	return mediaMap, nil
//}

// getPendingFeedMediaInfo 获取待用的动态媒体信息
func (f *FeedController) getPendingFeedMediaInfo(qid int64) (map[int64]models.UserGenMediasDBItem, error) {
	// 从database中获取
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	item := models.UserGenMediasDBItem{}
	query := `
		SELECT media_id, qid, scenario, media_object_key, file_size, status, created_at, extra
		FROM user_gen_medias
		WHERE qid = $1 AND scenario = 2 AND status = 0
	`
	rows, err := db.Query(query, qid)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}
	defer rows.Close()

	mediaMap := map[int64]models.UserGenMediasDBItem{}
	for rows.Next() {
		if sErr := rows.Scan(
			&item.MediaId,
			&item.Qid,
			&item.Scenario,
			&item.MediaObjectKey,
			&item.FileSize,
			&item.Status,
			&item.CreatedAt,
			&item.Extra,
		); sErr != nil {
			logrus.WithError(sErr).WithFields(logrus.Fields{
				"qid": qid,
			}).Error("getPendingFeedMediaInfo 扫描媒体信息失败")
			continue
		}
		mediaMap[item.MediaId] = item
	}

	return mediaMap, nil
}

func (f *FeedController) DeleteFeedUploadCache(qid int64) error {
	key := fmt.Sprintf("feed:media:upload:%v", qid)
	err := database.DeleteCache(key)
	if err != nil {
		return err
	}

	return nil
}

// stringVisTypeToInt 把字符串类型visType转成int类型，用于存储到数据库
func (f *FeedController) stringVisTypeToInt(visType string) int {
	switch visType {
	case "self":
		return models.VisTypeSelf
	case "all":
		return models.VisTypeAll
	case "whitelist":
		return models.VisTypeWhitelist
	case "blacklist":
		return models.VisTypeBlacklist
	default:
		return models.VisTypeSelf
	}
}

// intVisTypeToString 把int类型visType转成字符串类型，用于存储到数据库
func (f *FeedController) intVisTypeToString(visType int) string {
	switch visType {
	case models.VisTypeSelf:
		return "self"
	case models.VisTypeAll:
		return "all"
	case models.VisTypeWhitelist:
		return "whitelist"
	case models.VisTypeBlacklist:
		return "blacklist"
	default:
		return "self"
	}
}

type FeedAuditResult struct {
	OK     bool
	Reason string
}

// 审核动态
func (f *FeedController) auditFeed(req *CreateFeedRequest) (FeedAuditResult, error) {
	return FeedAuditResult{
		OK:     true,
		Reason: "",
	}, nil
}
