package controllers

import (
	"qing-feeds/utils"

	"github.com/kataras/iris/v12"
)

// HealthController handles health check endpoints
type HealthController struct{}

// NewHealthController creates a new health controller
func NewHealthController() *HealthController {
	return &HealthController{}
}

// Ping handles GET /ping - simple ping endpoint
func (h *HealthController) Ping(ctx iris.Context) {
	utils.SuccessWithMsg(ctx, map[string]string{
		"status": "pong",
	}, "feed service is running")
}
