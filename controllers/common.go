package controllers

import (
	"database/sql"
	"errors"
	"fmt"
	"qing-feeds/database"
	"qing-feeds/models"
	"qing-feeds/utils"
	"time"

	gonanoid "github.com/matoous/go-nanoid/v2"
)

const DefaultMediaDomain = "https://ethan0115.s3.bitiful.net"

const (
	FeedStatusDefault         = 0  // 0:未审核
	FeedStatusMachineApproved = 1  // 1:机审通过
	FeedStatusHumanApproved   = 2  // 2:人审通过
	FeedStatusMachineRejected = -1 // -1:机审不通过
	FeedStatusHumanRejected   = -2 // -2:人审不通过
	FeedStatusDeleted         = -3 // -3:删除
)
const (
	FeedGroupStatusDeleted = -1
	FeedGroupStatusNormal  = 0
)

const (
	SecureVisLevel1 = 1 // 仅自己可见（好友、陌生人查看主页、公域均不可见）
	SecureVisLevel2 = 2 // 自己、好友可见（陌生人查看主页不可见、公域不可见）
	SecureVisLevel3 = 3 // 自己、好友、陌生人查看主页可见（公域不可见）
	SecureVisLevel4 = 4 // 自己、好友、陌生人查看主页、公域均可见
)

type FeedMedia struct {
	MediaId   string `json:"media_id"`           // media id
	MediaType string `json:"media_type"`         // image video
	FileSize  int    `json:"file_size"`          // 文件大小 单位字节
	Duration  int    `json:"duration,omitempty"` // 视频/音频时长 单位毫秒
	Width     int    `json:"width,omitempty"`    // 图片/视频宽度
	Height    int    `json:"height,omitempty"`   // 图片/视频高度
}

// FeedLike 点赞表结构
type FeedLike struct {
	FeedID    int64 `json:"feed_id" db:"feed_id"`
	FeedQid   int64 `json:"feed_qid" db:"feed_qid"`
	Qid       int64 `json:"qid" db:"qid"`
	CreatedAt int64 `json:"created_at" db:"created_at"`
}

type UserRole struct {
	RoleName string `json:"role_name"`
	RoleId   string `json:"role_id"`
}

var qingUserRoles = []UserRole{
	{
		RoleName: "不选",
		RoleId:   "none",
	},
	{
		RoleName: "0",
		RoleId:   "0",
	},
	{
		RoleName: "0.1",
		RoleId:   "0.1",
	},
	{
		RoleName: "0.2",
		RoleId:   "0.2",
	},
	{
		RoleName: "0.3",
		RoleId:   "0.3",
	},
	{
		RoleName: "0.4",
		RoleId:   "0.4",
	},
	{
		RoleName: "0.5",
		RoleId:   "0.5",
	},
	{
		RoleName: "0.6",
		RoleId:   "0.6",
	},
	{
		RoleName: "0.7",
		RoleId:   "0.7",
	},
	{
		RoleName: "0.8",
		RoleId:   "0.8",
	},
	{
		RoleName: "0.9",
		RoleId:   "0.9",
	},
	{
		RoleName: "1",
		RoleId:   "1",
	},
	{
		RoleName: "Side",
		RoleId:   "side",
	},
	{
		RoleName: "~",
		RoleId:   "~",
	},
}

// findUserByPhone 通过手机号查找用户
func findUserByPhone(phone string) (*models.UserBasicInfoDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT qid, phone, custom_qid, username, avatar, role, birthday, region
		FROM user_basic_profiles
		WHERE phone = $1
	`

	userinfo := models.UserBasicInfoDBItem{}
	err := db.QueryRow(query, phone).Scan(&userinfo.Qid, &userinfo.Phone, &userinfo.CustomQid, &userinfo.Username, &userinfo.Avatar, &userinfo.Role, &userinfo.Birthday, &userinfo.Region)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return &userinfo, nil
}

// findUserByQid 通过qid查找用户
func findUserByQid(qid int64) (*models.UserBasicInfoDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT qid, phone, custom_qid, username, avatar, role, birthday, region
		FROM user_basic_profiles
		WHERE qid = $1
	`

	userinfo := models.UserBasicInfoDBItem{}
	err := db.QueryRow(query, qid).Scan(&userinfo.Qid, &userinfo.Phone, &userinfo.CustomQid, &userinfo.Username, &userinfo.Avatar, &userinfo.Role, &userinfo.Birthday, &userinfo.Region)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return &userinfo, nil
}

func roleIdToFloat(roleId string) float32 {
	return 0
}

func roleIdToRoleName(roleId string) string {
	return "0"
}

func floatToRoleId(role float32) string {
	return ""
}

// floatToRoleName 将float32角色值转换为字符串角色名
func floatToRoleName(floatRole float32) string {
	role := fmt.Sprintf("%.1f", floatRole)
	switch role {
	case "0.1", "0.2", "0.3", "0.4", "0.5", "0.6", "0.7", "0.8", "0.9":
		return role
	case "1.0":
		return "1"
	case "0.0":
		return "0"
	case "-1.0":
		return "none"
	case "2.0":
		return "side"
	case "3.0":
		return "~"
	}
	return "none"
}

// GetAvatarOriginUrl 获取头像的原始地址
func GetAvatarOriginUrl(objectKey string) string {
	// 后续可能会拼接一些参数
	return fmt.Sprintf("%v/%v", DefaultMediaDomain, objectKey)
}

// GenerateAvatarInfo 生成头像的信息
func GenerateAvatarInfo() (string, string) {
	randomId := ""
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyz", 14)
		if err == nil {
			randomId = id
			break
		}
	}

	if randomId == "" {
		randomId = utils.GetRandomString(14)
	}

	ms := time.Now().UnixMilli()
	return randomId, fmt.Sprintf("avatar/img-%v-%v", ms, randomId)
}

// generateFeedMediaObjectKey 生成动态媒体的objectKey
func generateFeedMediaObjectKey(prefix, suffix string) string {
	randomId := ""
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyz", 14)
		if err == nil {
			randomId = id
			break
		}
	}

	if randomId == "" {
		randomId = utils.GetRandomString(14)
	}

	ms := time.Now().UnixMilli()
	if suffix == "" {
		return fmt.Sprintf("feed/%v-%v-%v", prefix, ms, randomId)
	}

	return fmt.Sprintf("feed/%v-%v-%v.%v", prefix, ms, randomId, suffix)
}
