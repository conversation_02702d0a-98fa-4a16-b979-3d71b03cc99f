package controllers

import (
	"database/sql"
	"errors"
	"fmt"
	"qing-feeds/database"
	"qing-feeds/models"
	"qing-feeds/utils"
	"time"

	gonanoid "github.com/matoous/go-nanoid/v2"
)

const DefaultAvatarDomain = "https://ethan0115.s3.bitiful.net"

var qingUserRoles = []UserRole{
	{
		RoleName: "不选",
		RoleId:   "none",
	},
	{
		RoleName: "0",
		RoleId:   "0",
	},
	{
		RoleName: "0.1",
		RoleId:   "0.1",
	},
	{
		RoleName: "0.2",
		RoleId:   "0.2",
	},
	{
		RoleName: "0.3",
		RoleId:   "0.3",
	},
	{
		RoleName: "0.4",
		RoleId:   "0.4",
	},
	{
		RoleName: "0.5",
		RoleId:   "0.5",
	},
	{
		RoleName: "0.6",
		RoleId:   "0.6",
	},
	{
		RoleName: "0.7",
		RoleId:   "0.7",
	},
	{
		RoleName: "0.8",
		RoleId:   "0.8",
	},
	{
		RoleName: "0.9",
		RoleId:   "0.9",
	},
	{
		RoleName: "1",
		RoleId:   "1",
	},
	{
		RoleName: "Side",
		RoleId:   "side",
	},
	{
		RoleName: "~",
		RoleId:   "~",
	},
}

// findUserByPhone 通过手机号查找用户
func findUserByPhone(phone string) (*models.UserBasicInfo, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT qid, phone, custom_qid, username, avatar, role, birthday, region
		FROM user_basic_profiles
		WHERE phone = $1
	`

	userinfo := models.UserBasicInfo{}
	err := db.QueryRow(query, phone).Scan(&userinfo.Qid, &userinfo.Phone, &userinfo.CustomQid, &userinfo.Username, &userinfo.Avatar, &userinfo.Role, &userinfo.Birthday, &userinfo.Region)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return &userinfo, nil
}

// findUserByQid 通过qid查找用户
func findUserByQid(qid string) (*models.UserBasicInfo, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT qid, phone, custom_qid, username, avatar, role, birthday, region
		FROM user_basic_profiles
		WHERE qid = $1
	`

	userinfo := models.UserBasicInfo{}
	err := db.QueryRow(query, qid).Scan(&userinfo.Qid, &userinfo.Phone, &userinfo.CustomQid, &userinfo.Username, &userinfo.Avatar, &userinfo.Role, &userinfo.Birthday, &userinfo.Region)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 用户不存在
		}
		return nil, err
	}

	return &userinfo, nil
}

func roleIdToFloat(roleId string) float32 {
	return 0
}

func roleIdToRoleName(roleId string) string {
	return "0"
}

func floatToRoleId(role float32) string {
	return ""
}

// floatToRoleName 将float32角色值转换为字符串角色名
func floatToRoleName(floatRole float32) string {
	role := fmt.Sprintf("%.1f", floatRole)
	switch role {
	case "0.1", "0.2", "0.3", "0.4", "0.5", "0.6", "0.7", "0.8", "0.9":
		return role
	case "1.0":
		return "1"
	case "0.0":
		return "0"
	case "-1.0":
		return "none"
	case "2.0":
		return "side"
	case "3.0":
		return "~"
	}
	return "none"
}

// GetAvatarOriginUrl 获取头像的原始地址
func GetAvatarOriginUrl(objectKey string) string {
	// 后续可能会拼接一些参数
	return fmt.Sprintf("%v/%v", DefaultAvatarDomain, objectKey)
}

// GenerateAvatarInfo 生成头像的信息
func GenerateAvatarInfo() (string, string) {
	randomId := ""
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyz", 14)
		if err == nil {
			randomId = id
			break
		}
	}

	if randomId == "" {
		randomId = utils.GetRandomString(14)
	}

	ms := time.Now().UnixMilli()
	return randomId, fmt.Sprintf("avatar/img-%v-%v", ms, randomId)
}
