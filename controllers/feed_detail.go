package controllers

import (
	"encoding/json"
	"qing-feeds/models"
	"qing-feeds/utils"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

type GetFriendsFeedDetailResponse struct {
	MomentsFeedDetail
	Likes    []MomentsFeedDetailLike    `json:"likes"`
	Comments []MomentsFeedDetailComment `json:"comments"`
}

type MomentsFeedDetailLike struct {
	Qid      string `json:"qid"`
	Avatar   string `json:"avatar"`
	Username string `json:"username"`
}

type MomentsFeedDetailComment struct {
	CommentID        string `json:"comment_id"`
	Qid              string `json:"qid"`
	Username         string `json:"username"`
	Avatar           string `json:"avatar"`
	Content          string `json:"content"`
	CreatedAt        int64  `json:"created_at"`
	ReplyToCommentID string `json:"reply_to_comment_id"` // 回复的哪条评论
	ReplyToQid       string `json:"reply_to_qid"`        // 回复的哪个用户
	ReplyToUsername  string `json:"reply_to_username"`   // 回复的哪个用户的昵称
}

type MomentsFeedDetail struct {
	FeedID           string                   `json:"feed_id"`
	FeedQid          string                   `json:"feed_qid"`
	FeedUserAvatar   string                   `json:"feed_user_avatar"`
	FeedUsername     string                   `json:"feed_username"`
	FeedUsernameNote string                   `json:"feed_username_note"`
	Content          string                   `json:"content"`
	VisType          string                   `json:"vis_type"` // self:私密 all:公开 whitelist:部分人可见 blacklist:部分人不可见
	Public           bool                     `json:"public"`
	Location         FeedLocation             `json:"location"`
	CreatedAt        int64                    `json:"created_at"` // 秒
	UpdateAt         int64                    `json:"update_at"`
	Liked            bool                     `json:"liked"`      // 是否已点赞
	LikeCount        int64                    `json:"like_count"` // 点赞数
	Images           []FriendsFeedDetailImage `json:"images"`
	Videos           []FriendsFeedDetailVideo `json:"videos"`
}

type FriendsFeedDetailImage struct {
	Width         int    `json:"width"`           // 图片/视频宽度
	Height        int    `json:"height"`          // 图片/视频高度
	ImageUrl      string `json:"image_url"`       // 图片地址
	ImageThumbUrl string `json:"image_thumb_url"` // 缩略图地址
}

type FriendsFeedDetailVideo struct {
	Width         int    `json:"width"`           // 图片/视频宽度
	Height        int    `json:"height"`          // 图片/视频高度
	VideoUrl      string `json:"video_url"`       // 图片地址
	VideoThumbUrl string `json:"video_thumb_url"` // 缩略图地址
}

// GetMomentsFeedDetail 处理 GET /api/feeds/moments/:feed_id
// 查看好友圈动态详情
func (f *FeedController) GetMomentsFeedDetail(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))

	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("GetMomentsFeedDetail 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("GetMomentsFeedDetail 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 查询动态详情
	feedDetail, err := f.getFeedWithMediasByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	// 检查动态是否存在
	if feedDetail == nil {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("动态不存在")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 检查动态是否已删除
	if feedDetail.Status == FeedStatusDeleted {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("动态已删除")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 检查查看权限 只有自己、好友可查看，其他人不可查看
	isSelf := feedDetail.FeedQid == qid // 是否是主态
	isFriend := false                   // 是否是好友
	if isSelf == false {
		isFriend, err = f.isFriend(qid, feedDetail.FeedQid)
		if err != nil {
			logrus.WithFields(logrus.Fields{
				"feed_id":  feedID,
				"qid":      qid,
				"feed_qid": feedDetail.FeedQid,
			}).Error("检查是否是好友失败")
			utils.InternalError(ctx, "")
			return
		}
		if isFriend == false {
			logrus.WithFields(logrus.Fields{
				"feed_id":  feedID,
				"qid":      qid,
				"feed_qid": feedDetail.FeedQid,
			}).Error("不是好友，也不是主态，无权限查看")
			utils.InvalidRequestError(ctx, "")
			return
		}
	}

	// TODO 如果处于未审核状态，需要，判断分级可见配置 只有处于已审核状态的动态或者符合分级可见配置的，才能查看，其他状态不可见
	if feedDetail.Status <= 0 {
		logrus.WithFields(logrus.Fields{
			"feed_id":     feedID,
			"qid":         qid,
			"feed_qid":    feedDetail.FeedQid,
			"feed_status": feedDetail.Status,
		}).Error("动态状态异常，不允许查看")
		utils.InvalidRequestError(ctx, "")
	}

	// 是否是同步到日常的动态
	public := feedDetail.Public

	// 如果是公域动态，直接返回
	if public {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("这是私域动态详情接口，不能请求公域动态")
		return
	}

	// 私域主态
	response, err := f.getPrivateRegionFeedDetailResponse(qid, feedDetail)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("获取私域主态动态详情失败")
		utils.InternalError(ctx, "")
		return
	}
	utils.SuccessWithMsg(ctx, response, "")
	return
}

// 获取【私域】动态详情响应 返回所有好友点赞列表，好友评论
func (f *FeedController) getPrivateRegionFeedDetailResponse(requestQid int64, feedDetail *models.FeedDetailWithMediasDBItem) (*GetFriendsFeedDetailResponse, error) {
	//isSelf := feedDetail.FeedQid == requestQid // 是否是主态

	var location FeedLocation
	if feedDetail.Location.Valid {
		_ = json.Unmarshal([]byte(feedDetail.Location.String), &location)
	}

	// 不用获取动态中的媒体，因为在 feedDetail *models.FeedDetailWithMediasDBItem

	// 分离图片和视频
	images, videos := splitMedias(feedDetail.Medias)

	// TODO 获取动态发布人的信息 昵称头像
	// TODO 获取自己是否点赞
	// TODO 获取好友点赞列表
	// TODO 获取好友评论列表(一页即可)

	feedUserAvatar := ""                            // 从redis获取缓存
	feedUsername := ""                              // 从redis获取缓存
	liked := false                                  // 自己是否已点赞
	likes := make([]MomentsFeedDetailLike, 0)       // 全部好友点赞
	likeCount := int64(len(likes))                  // 总点赞数
	comments := make([]MomentsFeedDetailComment, 0) // 所有评论

	// 编码 feed_id feed_qid
	feedIDStr, _ := utils.EncodeFeedID(feedDetail.FeedID)
	feedQidStr, _ := utils.EncodeQID(feedDetail.FeedQid)
	detail := MomentsFeedDetail{
		FeedID:         feedIDStr,
		FeedQid:        feedQidStr,
		FeedUserAvatar: feedUserAvatar,
		FeedUsername:   feedUsername,
		Content:        feedDetail.Content,
		VisType:        f.intVisTypeToString(feedDetail.VisType),
		Public:         feedDetail.Public,
		Location:       location,
		CreatedAt:      feedDetail.CreatedAt,
		UpdateAt:       feedDetail.UpdateAt,
		Liked:          liked,
		LikeCount:      likeCount,
		Images:         images,
		Videos:         videos,
	}
	response := GetFriendsFeedDetailResponse{
		MomentsFeedDetail: detail,
		Likes:             likes,
		Comments:          comments,
	}

	return &response, nil
}

// 获取【公域】动态详情响应 返回所有好友点赞列表、总点赞数，无评论
func (f *FeedController) getPublicRegionFeedDetailResponse(requestQid string, feedDetail *models.FeedDetailWithMediasDBItem) (*GetFriendsFeedDetailResponse, error) {
	//isSelf := feedDetail.FeedQid == requestQid // 是否是主态

	var location *FeedLocation
	if feedDetail.Location.Valid {
		_ = json.Unmarshal([]byte(feedDetail.Location.String), &location)
	}

	// 获取动态中的媒体
	medias, err := f.getFeedMedias(feedDetail.FeedID)
	if err != nil {
		return nil, err
	}

	// 分离图片和视频
	images, videos := splitMedias(medias)

	// TODO 获取动态发布人的信息 昵称头像
	// TODO 获取自己是否点赞
	// TODO 获取好友点赞列表
	// TODO 获取评论列表

	feedUserAvatar := ""                      // 从redis获取缓存
	feedUsername := ""                        // 从redis获取缓存
	liked := false                            // 自己是否已点赞
	likes := make([]MomentsFeedDetailLike, 0) // 所有好友点赞列表
	likeCount := int64(0)                     // 总点赞数(包含公域 + 私域)

	// 编码 feed_id feed_qid
	feedIDStr, _ := utils.EncodeFeedID(feedDetail.FeedID)
	feedQidStr, _ := utils.EncodeQID(feedDetail.FeedQid)
	detail := MomentsFeedDetail{
		FeedID:         feedIDStr,
		FeedQid:        feedQidStr,
		FeedUserAvatar: feedUserAvatar,
		FeedUsername:   feedUsername,
		Content:        feedDetail.Content,
		VisType:        f.intVisTypeToString(feedDetail.VisType),
		Public:         feedDetail.Public,
		Location:       location,
		CreatedAt:      feedDetail.CreatedAt,
		UpdateAt:       feedDetail.UpdateAt,
		Liked:          liked,
		LikeCount:      likeCount,
		Images:         images,
		Videos:         videos,
	}
	response := GetFriendsFeedDetailResponse{
		MomentsFeedDetail: detail,
		Likes:             likes,
		Comments:          make([]MomentsFeedDetailComment, 0), // 不需要返回评论
	}

	return &response, nil
}

// 把medias分割成图片和视频
func splitMedias(medias []models.FeedMediaDBItem) ([]FriendsFeedDetailImage, []FriendsFeedDetailVideo) {
	images := make([]FriendsFeedDetailImage, 0)
	videos := make([]FriendsFeedDetailVideo, 0)

	for _, media := range medias {
		if media.MediaType == models.MediaTypeImage {
			images = append(images, FriendsFeedDetailImage{
				Width:         media.Width,
				Height:        media.Height,
				ImageUrl:      utils.GetOriginFeedMediaUrl(media.MediaObjectKey),
				ImageThumbUrl: utils.GetSmallFeedImageUrl(media.MediaObjectKey),
			})
		} else if media.MediaType == models.MediaTypeVideo {
			videos = append(videos, FriendsFeedDetailVideo{
				Width:    media.Width,
				Height:   media.Height,
				VideoUrl: utils.GetMiddleFeedVideoUrl(media.MediaObjectKey),
				//VideoThumbUrl: media.ThumbnailObjectKey,
			})
		}
	}

	return images, videos
}

// GetExploreFeedDetail 处理 GET /api/feeds/explore/:feed_id
// 查看公域动态详情
func (f *FeedController) GetExploreFeedDetail(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))

	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("GetMomentsFeedDetail 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("GetMomentsFeedDetail 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 查询动态详情
	feedDetail, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feedDetail == nil {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("动态不存在")
		utils.InvalidRequestError(ctx, "")
		return
	}

	if feedDetail.Status == FeedStatusDeleted {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("动态已删除")
		utils.InvalidRequestError(ctx, "")
		return
	}

	//if feedDetail.FeedQid != qid {
	//	return
	//}

	//utils.SuccessWithMsg(ctx, feed, "")
}
