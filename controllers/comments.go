package controllers

import "github.com/kataras/iris/v12"

// CreateMomentsFeedComment
// 评论私域动态
func (f *FeedController) CreateMomentsFeedComment(ctx iris.Context) {

}

// GetMomentsFeedComments
// 查看私域动态评论列表
func (f *FeedController) GetMomentsFeedComments(ctx iris.Context) {
	// 需求上不太会出现获取一个公域动态的评论列表的情况，因为本接口是获取私域动态评论的接口，所以这里不处理
}

// CreateExploreFeedComment
// 评论公域动态
func (f *FeedController) CreateExploreFeedComment(ctx iris.Context) {

}

// GetExploreFeedComments
// 查看公域动态评论列表
func (f *FeedController) GetExploreFeedComments(ctx iris.Context) {

}
