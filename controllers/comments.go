package controllers

import (
	"database/sql"
	"fmt"
	"qing-feeds/utils"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// CreateMomentsCommentRequest 创建评论请求
type CreateMomentsCommentRequest struct {
	Content          string `json:"content" validate:"required"`
	ReplyToCommentID string `json:"reply_to_comment_id,omitempty"`
	ReplyToQid       string `json:"reply_to_qid,omitempty"`
}

type CreateCommentResponse struct {
	CommentID string `json:"comment_id"`
}

// CreateMomentsFeedComment
// 评论私域动态
func (f *FeedController) CreateMomentsFeedComment(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))
	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("CreateMomentsFeedComment 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("CreateMomentsFeedComment 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	var req CreateMomentsCommentRequest
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("CreateMomentsFeedComment 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 处理参数
	req.Content = strings.TrimSpace(req.Content)
	req.ReplyToCommentID = strings.TrimSpace(req.ReplyToCommentID)
	req.ReplyToQid = strings.TrimSpace(req.ReplyToQid)

	// 验证请求参数
	if req.Content == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("评论内容不能为空")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 验证动态是否存在
	feedDetail, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feedDetail == nil {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("动态不存在")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 本接口是私域动态评论接口，公域动态请使用公域接口
	if feedDetail.Public {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("此动态是公域动态，不能使用私域评论接口")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 检查权限：私域动态需要是好友关系
	// 如果不是动态发布者本人，需要检查好友关系
	if feedDetail.FeedQid != qid {
		isFriend, err := f.checkUserRelationship(qid, feedDetail.FeedQid)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":      qid,
				"feed_qid": feedDetail.FeedQid,
			}).Error("检查用户关系失败")
			utils.InternalError(ctx, "")
			return
		}

		if !isFriend {
			logrus.WithFields(logrus.Fields{
				"qid":      qid,
				"feed_qid": feedDetail.FeedQid,
			}).Error("不是好友，无权限评论此动态")
			utils.InvalidRequestError(ctx, "")
			return
		}
	}

	// 如果是回复评论，验证被回复的评论是否存在，且qid匹配
	replyToCommentID := sql.NullInt64{Int64: 0, Valid: false}
	replyToQid := sql.NullInt64{Int64: 0, Valid: false}

	if req.ReplyToCommentID != "" && req.ReplyToQid != "" {
		// 解码评论ID
		rptcid, cErr := utils.DecodeFeedCommentID(req.ReplyToCommentID)
		if cErr != nil || rptcid <= 0 {
			logrus.WithError(cErr).WithFields(logrus.Fields{
				"comment_id": req.ReplyToCommentID,
				"feedID":     feedID,
				"qid":        qid,
				"rptcid":     rptcid,
			}).Error("解码评论ID失败")
			utils.InternalError(ctx, "")
			return
		}

		rptqid, rErr := utils.DecodeQID(req.ReplyToQid)
		if rErr != nil || rptqid <= 0 {
			logrus.WithError(rErr).WithFields(logrus.Fields{
				"comment_id": req.ReplyToCommentID,
				"feedID":     feedID,
				"qid":        qid,
				"rptqid":     rptqid,
			}).Error("解码评论者qid失败")
			utils.InternalError(ctx, "")
			return
		}

		replyComment, err := f.getCommentByID(rptcid)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"comment_id": req.ReplyToCommentID,
				"qid":        qid,
			}).Error("查询被回复评论失败")
			utils.InternalError(ctx, "")
			return
		}

		if replyComment == nil || replyComment.FeedID != feedID || replyComment.Qid != rptqid {
			logrus.WithFields(logrus.Fields{
				"comment_id": req.ReplyToCommentID,
				"qid":        qid,
			}).Error("被回复的评论不存在，或者动态id不匹配，或者评论qid不匹配")
			utils.InvalidRequestError(ctx, "")
			return
		}

		replyToCommentID = sql.NullInt64{Int64: rptcid, Valid: true}
		replyToQid = sql.NullInt64{Int64: rptqid, Valid: true}
	}

	// 创建评论
	commentID := utils.GenerateFeedCommentID()
	commentIDStr, _ := utils.EncodeFeedCommentID(commentID)
	now := time.Now().UnixMilli()
	parentId := sql.NullInt64{Int64: 0, Valid: false}

	if iErr := f.insertComment(commentID, feedID, feedDetail.FeedQid, qid, req.Content, parentId, replyToCommentID, replyToQid, now); iErr != nil {
		logrus.WithError(iErr).WithFields(logrus.Fields{
			"feed_id":    feedID,
			"comment_id": commentID,
			"qid":        qid,
		}).Error("创建评论失败")
		utils.InternalError(ctx, "")
		return
	}

	// 构建响应
	response := &CreateCommentResponse{
		CommentID: commentIDStr,
	}

	logrus.WithFields(logrus.Fields{
		"feed_id":    feedID,
		"comment_id": commentID,
		"qid":        qid,
	}).Info("私域动态评论创建成功")

	utils.SuccessWithMsg(ctx, response, "")
}

type MomentsFeedComment struct {
	CommentID        string `json:"comment_id"`
	Qid              string `json:"qid"`
	Username         string `json:"username"`
	Content          string `json:"content"`
	ReplyToCommentId string `json:"reply_to_comment_id"`
	ReplyToQid       string `json:"reply_to_qid"`
	CreatedAt        int64  `json:"created_at"`
}

type GetMomentsFeedCommentsResponse struct {
	List       []MomentsFeedComment `json:"list"`
	NextCursor string               `json:"next_cursor"`
}

const DBFeedCommentPageCount = 20 // 每次从数据库查询多少条动态

// GetMomentsFeedComments
// 查看私域动态评论列表
func (f *FeedController) GetMomentsFeedComments(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))

	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("GetMomentsFeedComments 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("GetMomentsFeedComments 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 获取分页参数
	cursor := strings.TrimSpace(ctx.URLParam("cursor"))
	ok, belowCreatedAt := toValidCommentCursor(cursor)
	if ok == false {
		logrus.WithFields(logrus.Fields{
			"qid":    qid,
			"cursor": cursor,
		}).Error("无效的翻页标识")
		belowCreatedAt = 0 // 重置
	}

	// 验证动态是否存在
	feed, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feed == nil {
		utils.InvalidRequestError(ctx, "动态不存在")
		return
	}

	// 本接口是私域动态评论接口，公域动态请使用公域接口
	if feed.Public {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("此动态是公域动态，不能使用私域评论接口")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 检查权限：私域动态需要是好友关系
	// 如果不是动态发布者本人，需要检查好友关系
	if feed.FeedQid != qid {
		isFriend, err := f.checkUserRelationship(qid, feed.FeedQid)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"qid":      qid,
				"feed_qid": feed.FeedQid,
			}).Error("检查用户关系失败")
			utils.InternalError(ctx, "")
			return
		}

		if !isFriend {
			logrus.WithFields(logrus.Fields{
				"qid":      qid,
				"feed_qid": feed.FeedQid,
			}).Error("不是好友，无权限查看此动态的评论")
			utils.InvalidRequestError(ctx, "")
			return
		}
	}

	// 查询评论列表 只有【我的好友的评论】才能【被我看到】
	comments, err := f.getMomentsFeedComments(qid, feedID, belowCreatedAt, DBFeedCommentPageCount)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询评论列表失败")
		utils.InternalError(ctx, "")
		return
	}

	// 构建响应
	list := make([]MomentsFeedComment, 0)
	nextCursor := ""

	// TODO 从redis获取用户资料
	for _, comment := range comments {
		commentIDStr, _ := utils.EncodeFeedCommentID(comment.CommentID)
		qidStr, _ := utils.EncodeQID(comment.Qid)

		// 回复的评论ID和回复的用户ID可能为空
		replyToCommentIDStr := ""
		replyToQidStr := ""
		if comment.ReplyToCommentID.Valid {
			replyToCommentIDStr, _ = utils.EncodeFeedCommentID(comment.ReplyToCommentID.Int64)
		}
		if comment.ReplyToQid.Valid {
			replyToQidStr, _ = utils.EncodeQID(comment.ReplyToQid.Int64)
		}

		list = append(list, MomentsFeedComment{
			CommentID:        commentIDStr,
			Qid:              qidStr,
			Username:         "",
			Content:          comment.Content,
			ReplyToCommentId: replyToCommentIDStr,
			ReplyToQid:       replyToQidStr,
			CreatedAt:        comment.CreatedAt,
		})

		nextCursor = fmt.Sprintf("t:%d", comment.CreatedAt)
		if len(list) >= DBFeedCommentPageCount {
			break
		}
	}

	// 从数据库中获取不足一页，说明没有更多数据了
	if len(comments) < DBFeedCommentPageCount {
		nextCursor = ""
	}

	response := &GetMomentsFeedCommentsResponse{
		List:       list,
		NextCursor: nextCursor,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

// 如果cursor是按照时间戳来的，那么以 t: 开头(t:1754023695000), 后续如果更精精准，可以以 comment_id和时间组合的 以 ft:fc_1836496DGCSGVG:1754023695000 格式
func toValidCommentCursor(cursor string) (bool, int64) {
	if cursor == "" {
		return true, 0
	}

	if strings.HasPrefix(cursor, "t:") && len(cursor) == 15 {
		ms, err := strconv.ParseInt(cursor[2:], 10, 64)
		if err != nil {
			return false, 0
		}
		// 小于20250801也不对
		if ms <= 1753977600000 {
			return false, 0
		}
		return true, ms
	}
	return false, 0
}

// CreateExploreCommentRequest 创建评论请求
type CreateExploreCommentRequest struct {
	Content          string `json:"content" validate:"required"`
	ParentId         string `json:"parent_id,omitempty"`
	ReplyToCommentID string `json:"reply_to_comment_id,omitempty"`
	ReplyToQid       string `json:"reply_to_qid,omitempty"`
}

// CreateExploreFeedComment
// 评论公域动态
func (f *FeedController) CreateExploreFeedComment(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))
	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("CreateExploreFeedComment 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("CreateExploreFeedComment 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	var req CreateExploreCommentRequest
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("CreateExploreFeedComment 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 处理参数
	req.Content = strings.TrimSpace(req.Content)
	req.ReplyToCommentID = strings.TrimSpace(req.ReplyToCommentID)
	req.ParentId = strings.TrimSpace(req.ParentId)
	req.ReplyToQid = strings.TrimSpace(req.ReplyToQid)

	// 验证请求参数
	if req.Content == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("评论内容不能为空")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 验证动态是否存在
	feed, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feed == nil {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("动态不存在")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 本接口是公域动态评论接口，私域动态请使用私域接口
	if feed.Public == false {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("此动态是私域动态，不能使用公域评论接口")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// TODO 如果存在 req.ParentId，需要验证req.ParentId是否是一个存在的一级评论，且只有 req.ParentId 存在，才需要处理req.ReplyToCommentID

	// 如果是回复评论，验证被回复的评论是否存在，且qid匹配
	replyToCommentID := sql.NullInt64{Int64: 0, Valid: false}
	replyToQid := sql.NullInt64{Int64: 0, Valid: false}

	if req.ReplyToCommentID != "" && req.ReplyToQid != "" {
		// 解码评论ID
		rptcid, cErr := utils.DecodeFeedCommentID(req.ReplyToCommentID)
		if cErr != nil || rptcid <= 0 {
			logrus.WithError(cErr).WithFields(logrus.Fields{
				"comment_id": req.ReplyToCommentID,
				"feedID":     feedID,
				"qid":        qid,
				"rptcid":     rptcid,
			}).Error("解码评论ID失败")
			utils.InternalError(ctx, "")
			return
		}

		rptqid, rErr := utils.DecodeQID(req.ReplyToQid)
		if rErr != nil || rptqid <= 0 {
			logrus.WithError(rErr).WithFields(logrus.Fields{
				"comment_id": req.ReplyToCommentID,
				"feedID":     feedID,
				"qid":        qid,
				"rptqid":     rptqid,
			}).Error("解码评论者qid失败")
			utils.InternalError(ctx, "")
			return
		}

		replyComment, err := f.getCommentByID(rptcid)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"comment_id": req.ReplyToCommentID,
				"qid":        qid,
			}).Error("查询被回复评论失败")
			utils.InternalError(ctx, "")
			return
		}

		if replyComment == nil || replyComment.FeedID != feedID || replyComment.Qid != rptqid {
			logrus.WithFields(logrus.Fields{
				"comment_id": req.ReplyToCommentID,
				"qid":        qid,
			}).Error("被回复的评论不存在，或者动态id不匹配，或者评论qid不匹配")
			utils.InvalidRequestError(ctx, "")
			return
		}

		replyToCommentID = sql.NullInt64{Int64: rptcid, Valid: true}
		replyToQid = sql.NullInt64{Int64: rptqid, Valid: true}
	}

	// 创建评论
	commentID := utils.GenerateFeedCommentID()
	commentIDStr, _ := utils.EncodeFeedCommentID(commentID)
	now := time.Now().UnixMilli()
	parentId := sql.NullInt64{Int64: 0, Valid: false}

	if iErr := f.insertComment(commentID, feedID, feed.FeedQid, qid, req.Content, parentId, replyToCommentID, replyToQid, now); iErr != nil {
		logrus.WithError(iErr).WithFields(logrus.Fields{
			"feed_id":    feedID,
			"comment_id": commentID,
			"qid":        qid,
		}).Error("创建评论失败")
		utils.InternalError(ctx, "")
		return
	}

	// 构建响应
	response := &CreateCommentResponse{
		CommentID: commentIDStr,
	}

	logrus.WithFields(logrus.Fields{
		"feed_id":    feedID,
		"comment_id": commentID,
		"qid":        qid,
	}).Info("私域动态评论创建成功")

	utils.SuccessWithMsg(ctx, response, "")
}

type ExploreFeedComment struct {
	CommentID        string               `json:"comment_id"`
	Qid              string               `json:"qid"`
	Username         string               `json:"username"`
	Content          string               `json:"content"`
	ParentId         string               `json:"parent_id"`
	ReplyToCommentId string               `json:"reply_to_comment_id"`
	ReplyToQid       string               `json:"reply_to_qid"`
	CreatedAt        int64                `json:"created_at"`
	Replies          []ExploreFeedComment `json:"replies"`     // 一级评论的回复列表
	ReplyCount       int64                `json:"reply_count"` // 一级评论的回复数
}

type GetExploreFeedCommentsResponse struct {
	List       []ExploreFeedComment `json:"list"`
	NextCursor string               `json:"next_cursor"`
}

// GetExploreFeedComments
// 查看公域动态评论列表
func (f *FeedController) GetExploreFeedComments(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))

	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("GetExploreFeedComments 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("GetExploreFeedComments 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 获取分页参数
	cursor := strings.TrimSpace(ctx.URLParam("cursor"))
	ok, belowCreatedAt := toValidCommentCursor(cursor)
	if ok == false {
		logrus.WithFields(logrus.Fields{
			"qid":    qid,
			"cursor": cursor,
		}).Error("无效的翻页标识")
		belowCreatedAt = 0 // 重置
	}

	// 验证动态是否存在
	feed, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feed == nil {
		utils.InvalidRequestError(ctx, "动态不存在")
		return
	}

	// 本接口是公域动态评论接口，私域动态请使用私域接口
	if feed.Public == false {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("此动态是私域动态，不能使用公域评论接口")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 查询评论列表，查询一级评论，每个一级评论携带一个二级评论和二级评论数
	comments, err := f.getExploreFeedComments(qid, feedID, belowCreatedAt, DBFeedCommentPageCount)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询评论列表失败")
		utils.InternalError(ctx, "")
		return
	}

	// 构建响应
	list := make([]MomentsFeedComment, 0)
	nextCursor := ""

	// TODO 从redis获取用户资料
	for _, comment := range comments {
		commentIDStr, _ := utils.EncodeFeedCommentID(comment.CommentID)
		qidStr, _ := utils.EncodeQID(comment.Qid)

		// 回复的评论ID和回复的用户ID可能为空
		replyToCommentIDStr := ""
		replyToQidStr := ""
		if comment.ReplyToCommentID.Valid {
			replyToCommentIDStr, _ = utils.EncodeFeedCommentID(comment.ReplyToCommentID.Int64)
		}
		if comment.ReplyToQid.Valid {
			replyToQidStr, _ = utils.EncodeQID(comment.ReplyToQid.Int64)
		}

		list = append(list, MomentsFeedComment{
			CommentID:        commentIDStr,
			Qid:              qidStr,
			Username:         "",
			Content:          comment.Content,
			ReplyToCommentId: replyToCommentIDStr,
			ReplyToQid:       replyToQidStr,
			CreatedAt:        comment.CreatedAt,
		})

		nextCursor = fmt.Sprintf("t:%d", comment.CreatedAt)
		if len(list) >= DBFeedCommentPageCount {
			break
		}
	}

	// 从数据库中获取不足一页，说明没有更多数据了
	if len(comments) < DBFeedCommentPageCount {
		nextCursor = ""
	}

	response := &GetMomentsFeedCommentsResponse{
		List:       list,
		NextCursor: nextCursor,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}
