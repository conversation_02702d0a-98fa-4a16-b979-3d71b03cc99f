package controllers

import (
	"qing-feeds/utils"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// DeleteFeed 删除动态
func (f *FeedController) DeleteFeed(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))

	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("DeleteFeed 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("DeleteFeed 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 验证动态是否存在且属于当前用户
	feedDetail, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feedDetail == nil {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("动态不存在")
		utils.InvalidRequestError(ctx, "")
		return
	}

	if feedDetail.FeedQid != qid {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("无权限删除此动态，动态不属于当前用户")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 如果动态已删除，直接返回
	if feedDetail.Status == FeedStatusDeleted {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("删除一个已删除的动态，直接返回成功，但什么也不做")
		utils.SuccessWithMsg(ctx, nil, "")
		return
	}

	// 软删除动态
	if err := f.softDeleteFeed(feedID); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id":  feedID,
			"feed_qid": qid,
		}).Error("修改动态状态为已删除时失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"feed_id":  feedID,
		"feed_qid": qid,
	}).Info("动态删除成功")

	utils.SuccessWithMsg(ctx, nil, "")
}
