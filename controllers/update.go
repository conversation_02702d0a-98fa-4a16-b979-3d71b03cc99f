package controllers

import (
	"qing-feeds/database"
	"qing-feeds/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// FeedMediaRequest 动态媒体请求
type FeedMediaRequest struct {
	MediaID            string                 `json:"media_id" validate:"required"`
	MediaType          int                    `json:"media_type" validate:"required,min=1,max=2"`
	MediaOrder         int                    `json:"media_order" validate:"required"`
	MediaObjectKey     string                 `json:"media_object_key" validate:"required"`
	ThumbnailObjectKey string                 `json:"thumbnail_object_key,omitempty"`
	ThumbHash          string                 `json:"thumb_hash,omitempty"`
	FileSize           int                    `json:"file_size" validate:"required"`
	Duration           *int                   `json:"duration,omitempty"`
	Width              int                    `json:"width" validate:"required"`
	Height             int                    `json:"height" validate:"required"`
	Exif               map[string]interface{} `json:"exif,omitempty"`
	Extra              map[string]interface{} `json:"extra,omitempty"`
}

// UpdateFeedRequest 修改动态请求
type UpdateFeedRequest struct {
	Content   *string                 `json:"content,omitempty"`
	VisType   *int                    `json:"vis_type,omitempty"`
	GroupIDs  []string                `json:"group_ids,omitempty"`
	Location  *map[string]interface{} `json:"location,omitempty"`
	MediaList []FeedMediaRequest      `json:"media_list,omitempty"`
}

// UpdateFeed 处理 PUT /api/feeds/:feed_id
// 修改动态
func (f *FeedController) UpdateFeed(ctx iris.Context) {
	qid := ctx.Values().GetString("qid")
	feedID := strings.TrimSpace(ctx.Params().Get("feed_id"))

	var req UpdateFeedRequest
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("UpdateFeed 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 验证动态是否存在且属于当前用户
	feed, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feed == nil {
		utils.InvalidRequestError(ctx, "动态不存在")
		return
	}

	if feed.FeedQid != qid {
		utils.InvalidRequestError(ctx, "无权限修改此动态")
		return
	}

	if feed.Status == FeedStatusDeleted {
		utils.InvalidRequestError(ctx, "动态已删除")
		return
	}

	// 开始数据库事务
	db := database.GetDB()
	tx, err := db.Begin()
	if err != nil {
		logrus.WithError(err).Error("开始事务失败")
		utils.InternalError(ctx, "")
		return
	}
	defer tx.Rollback()

	now := time.Now().UnixMilli()

	// 更新动态内容
	if err := f.updateFeed(tx, feedID, &req, now); err != nil {
		logrus.WithError(err).Error("更新动态失败")
		utils.InternalError(ctx, "")
		return
	}

	// 如果修改了可见性，更新可见性记录
	if req.VisType != nil {
		// 删除旧的可见性记录
		if err := f.deleteFeedVisibilities(tx, feedID); err != nil {
			logrus.WithError(err).Error("删除旧可见性记录失败")
			utils.InternalError(ctx, "")
			return
		}

		// 插入新的可见性记录
		//if err := f.insertFeedVisibilities(tx, feedID, *req.VisType, req.GroupIDs, now); err != nil {
		//	logrus.WithError(err).Error("插入新可见性记录失败")
		//	utils.InternalError(ctx, "")
		//	return
		//}
	}

	// 如果修改了媒体，更新媒体记录
	if req.MediaList != nil {
		// 删除旧的媒体记录
		if err := f.deleteFeedMedias(tx, feedID); err != nil {
			logrus.WithError(err).Error("删除旧媒体记录失败")
			utils.InternalError(ctx, "")
			return
		}

		// 插入新的媒体记录
		if len(req.MediaList) > 0 {
			if err := f.insertFeedMedias(tx, feedID, qid, req.MediaList, now); err != nil {
				logrus.WithError(err).Error("插入新媒体记录失败")
				utils.InternalError(ctx, "")
				return
			}
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		logrus.WithError(err).Error("提交事务失败")
		utils.InternalError(ctx, "")
		return
	}

	// 如果修改了可见性，重新提交用户可见动态任务
	if req.VisType != nil {
		//utils.SubmitUserVisibleFeedsTaskAsync(feedID, qid, *req.VisType, req.GroupIDs)
	}

	logrus.WithFields(logrus.Fields{
		"feed_id":  feedID,
		"feed_qid": qid,
	}).Info("动态修改成功")

	utils.SuccessWithMsg(ctx, nil, "")
}
