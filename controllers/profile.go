package controllers

import (
	"database/sql"
	"errors"
	"fmt"
	"qing-feeds/database"
	"qing-feeds/utils"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// ProfileController 处理用户资料相关的端点
type ProfileController struct{}

// NewProfileController 创建新的用户资料控制器
func NewProfileController() *ProfileController {
	return &ProfileController{}
}

// ProfileInitializeRequest 表示用户资料初始化请求的结构
type ProfileInitializeRequest struct {
	AvatarId string `json:"avatar_id" validate:"required"`
	Username string `json:"username" validate:"required"`
	RoleId   string `json:"role_id" validate:"required"`
}

// InitializeProfile 处理 POST /api/users/profile/initialize
// 初始化用户基本资料信息
func (p *ProfileController) InitializeProfile(ctx iris.Context) {
	deviceID := ctx.Values().GetString("x-device-id")

	var req ProfileInitializeRequest

	// 解析JSON请求体
	if err := ctx.ReadJSON(&req); err != nil {
		logrus.WithError(err).Error("InitializeProfile 解析请求参数失败")
		utils.InvalidRequestParamsError(ctx, "")
		return
	}

	// 修剪字段的空白字符
	req.AvatarId = strings.TrimSpace(req.AvatarId)
	req.Username = strings.TrimSpace(req.Username)
	req.RoleId = strings.TrimSpace(req.RoleId)

	// 验证必填字段
	if req.AvatarId == "" {
		utils.InvalidRequestError(ctx, "头像URL是必填项")
		return
	}

	if req.Username == "" {
		utils.InvalidRequestError(ctx, "用户昵称是必填项")
		return
	}

	// 验证role参数范围（0.0 ≤ role ≤ 1.0）
	floatRole, err := p.toFloatRole(req.RoleId)
	if err != nil {
		utils.InvalidRequestError(ctx, "用户角色无效")
		return
	}

	// 从中间件获取用户ID
	qid := ctx.Values().GetString("x-qid")

	// 获取用户资料
	userinfo, err := findUserByQid(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("InitializePassword findUserByQid 获取用户资料失败")

		utils.InternalError(ctx, "")
		return
	}

	if userinfo == nil {
		logrus.WithFields(logrus.Fields{
			"qid": qid,
		}).Error("InitializePassword findUserByQid 用户不存在")

		utils.InvalidRequestError(ctx, "")
		return
	}

	// 检查用户是否已经初始化过资料
	requiresProfileCompletion := userinfo.Avatar == "" && userinfo.Username == "" && userinfo.Role == -1
	if requiresProfileCompletion == false {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"avatar_id": req.AvatarId,
			"username":  req.Username,
			"role_id":   req.RoleId,
		}).Error("用户资料已经初始化过")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 检查AvatarId
	objectKey, err := p.getUploadAvatarInfo(qid, req.AvatarId, deviceID)
	if err != nil || objectKey == "" {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":       qid,
			"avatar_id": req.AvatarId,
			"username":  req.Username,
			"role_id":   req.RoleId,
			"objectKey": objectKey,
		}).Error("InitializeProfile getAvatarUrl 失败")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 更新用户的资料
	if err = p.updateUserBasicInfo(qid, objectKey, req.Username, floatRole); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":       qid,
			"avatar_id": req.AvatarId,
			"username":  req.Username,
			"role_id":   req.RoleId,
			"avatar":    objectKey,
		}).Error("更新用户资料失败")
		utils.InternalError(ctx, "")
		return
	}

	// 记录成功的资料初始化
	logrus.WithFields(logrus.Fields{
		"qid":       qid,
		"avatar_id": req.AvatarId,
		"username":  req.Username,
		"role_id":   req.RoleId,
		"avatar":    objectKey,
	}).Info("用户资料初始化成功")

	// 返回成功响应
	utils.SuccessWithMsg(ctx, nil, "")
}

type GetRolesResponse struct {
	List []UserRole `json:"list"`
}

type UserRole struct {
	RoleName string `json:"role_name"`
	RoleId   string `json:"role_id"`
}

// GetRoles 处理 POST /api/users/profile/roles
// 获取角色列表
func (p *ProfileController) GetRoles(ctx iris.Context) {
	response := GetRolesResponse{
		List: qingUserRoles,
	}
	// 返回成功响应
	utils.SuccessWithMsg(ctx, response, "")
}

// getUploadAvatarInfo 获取头像上传信息
func (p *ProfileController) getUploadAvatarInfo(qid, avatarId string, deviceId string) (string, error) {
	key := fmt.Sprintf("avatar:upload:%v:%v:%v", qid, avatarId, deviceId)
	objectKey, err := database.GetCache(key)
	if err != nil {
		return "", err
	}
	return objectKey, nil
}

// toFloatRole 将字符串角色转换为有效的float32角色值 side:2 ~:3 不选:-1
func (p *ProfileController) toFloatRole(role string) (float32, error) {
	switch role {
	case "0", "0.1", "0.2", "0.3", "0.4", "0.5", "0.6", "0.7", "0.8", "0.9", "1":
		fRole, _ := strconv.ParseFloat(role, 32)
		return float32(fRole), nil
	case "none":
		return -1, nil
	case "side":
		return 2, nil
	case "~":
		return 3, nil
	}
	return 0, errors.New("不支持的角色类型")
}

// updateUserBasicInfo 更新用户基本资料
func (p *ProfileController) updateUserBasicInfo(qid, avatar, username string, role float32) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		UPDATE user_basic_profiles
		SET avatar = $1, username = $2, role = $3
		WHERE qid = $4
	`

	_, err := db.Exec(
		query,
		avatar,
		username,
		role,
		qid,
	)

	if err != nil {
		return err
	}

	return nil
}
