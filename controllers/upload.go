package controllers

import (
	"database/sql"
	"encoding/json"
	"qing-feeds/database"
	"qing-feeds/s4"
	"qing-feeds/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

const MaxImageDataSize = 10 * 1024 * 1024  // 10M
const MaxVideoDataSize = 100 * 1024 * 1024 // 10M
const MinImageWidth = 1000
const MinImageHeight = 1000

type GetMediaUploadURLRequest struct {
	MediaType string `json:"media_type"` // image video
	FileType  string `json:"file_type"`  // jpg png
	FileSize  int64  `json:"file_size"`
	Width     int64  `json:"width"`
	Height    int64  `json:"height"`
}
type GetMediaUploadURLResponse struct {
	UploadURL string `json:"upload_url"`
	MediaId   string `json:"media_id"`
}

type FeedPendingMedia struct {
	MediaId   int64  `json:"media_id"`
	MediaType string `json:"media_type"`
	ObjectKey string `json:"object_key"`
	FileSize  int64  `json:"file_size"`
	Width     int64  `json:"width"`
	Height    int64  `json:"height"`
	DeviceId  string `json:"device_id"`
}

// GetMediaUploadURL 处理 GET /api/feeds/upload-media-url
// 获取上传头像的上传地址 限制：上限：最大不能超过10M，下限：最小不能低于像素1000x1000
func (f *FeedController) GetMediaUploadURL(ctx iris.Context) {
	// 从中间件获取用户ID（由CheckAuthorization中间件处理JWT后设置）
	qid, _ := ctx.Values().GetInt64("x-qid")
	deviceID := ctx.Values().GetString("x-device-id")

	var req GetMediaUploadURLRequest
	req.MediaType = strings.TrimSpace(ctx.URLParam("media_type"))
	req.FileType = strings.TrimSpace(ctx.URLParam("file_type"))
	req.FileSize, _ = ctx.URLParamInt64("file_size")
	req.Width, _ = ctx.URLParamInt64("width")
	req.Height, _ = ctx.URLParamInt64("height")

	if req.MediaType != "image" && req.MediaType != "video" {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL MediaType 无效")
		utils.InvalidRequestError(ctx, "")
		return
	}
	if req.FileSize <= 0 {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL FileSize 无效")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 图片最大不能超多10M
	if req.MediaType == "image" && req.FileSize > MaxImageDataSize {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL FileSize 图片大小超过限制")
		utils.InvalidRequestError(ctx, "图片大小超过限制")
		return
	}

	// 视频最大不能超多100M
	if req.MediaType == "video" && req.FileSize > MaxVideoDataSize {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL FileSize 视频大小超过限制")
		utils.InvalidRequestError(ctx, "视频大小超过限制")
		return
	}

	// 最小像素
	if req.Width < MinImageWidth || req.Height < MinImageHeight {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Error("GetMediaUploadURL Width Height 图片尺寸过小")
		utils.InvalidRequestError(ctx, "图片尺寸过小")
		return
	}

	// 获取用户资料
	userinfo, err := findUserByQid(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("GetMediaUploadURL findUserByQid 获取用户资料失败")

		utils.InternalError(ctx, "")
		return
	}

	if userinfo == nil {
		logrus.WithFields(logrus.Fields{
			"qid": qid,
		}).Error("GetMediaUploadURL findUserByQid 用户不存在")

		utils.InvalidRequestError(ctx, "")
		return
	}

	// 生成一个随机的avatar_id
	prefix, suffix := f.getFileTypeInfo(req.FileType)
	objectKey := generateFeedMediaObjectKey(prefix, suffix)

	// 获取上传地址
	uploadUrl := s4.GetUploadUrl(objectKey, req.FileSize)

	mediaId := utils.GenerateMediaID()
	mediaIdStr, err := utils.EncodeMediaID(mediaId)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"mediaId": mediaId,
		}).Error("GetMediaUploadURL EncodeMediaID 失败")
		utils.InternalError(ctx, "")
		return
	}

	feedMedia := &FeedPendingMedia{
		MediaId:   mediaId,
		MediaType: req.MediaType,
		ObjectKey: objectKey,
		FileSize:  req.FileSize,
		Width:     req.Width,
		Height:    req.Height,
		DeviceId:  deviceID,
	}

	// 保存
	err = f.saveUploadFeedMediaInfo(qid, feedMedia)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"mediaId": mediaId,
		}).Error("GetMediaUploadURL saveMediaUploadInfo 失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"qid":        qid,
		"file_type":  req.FileType,
		"file_size":  req.FileSize,
		"width":      req.Width,
		"height":     req.Height,
		"uploadUrl":  uploadUrl,
		"mediaId":    mediaId,
		"objectKey":  objectKey,
		"mediaIdStr": mediaIdStr,
	}).Debug("获取动态媒体文件的上传URL成功")

	response := &GetMediaUploadURLResponse{
		UploadURL: uploadUrl,
		MediaId:   mediaIdStr,
	}

	// 返回成功响应
	utils.SuccessWithMsg(ctx, response, "")
}

// getFileTypeInfo 获取文件的格式前后缀 img,jpg
func (f *FeedController) getFileTypeInfo(fileType string) (string, string) {
	if fileType == "" {
		return "file", ""
	}

	// 比如 image/png 返回png, 如果是png直接返回png
	if strings.Contains(fileType, "/") {
		value := fileType[strings.Index(fileType, "/")+1:]
		fileType = strings.TrimSpace(strings.Trim(value, "."))
	}

	if utils.IsImageType(fileType) {
		return "img", fileType
	}

	if utils.IsVideoType(fileType) {
		return "vid", fileType
	}

	return "file", ""
}

type UserGenFeedMediaExtra struct {
	DeviceId string `json:"device_id"`
}

// saveUploadFeedMediaInfo 保存动态媒体上传信息
func (f *FeedController) saveUploadFeedMediaInfo(qid int64, feedMedia *FeedPendingMedia) error {
	now := time.Now().UnixMilli()
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	scenario := 2 // 场景 1:设置头像 2:发布动态
	status := 0   //-2:已删除 -1:未通过审核 0:默认待用 1:已使用 2:已审核 （如果用户上传了3个图，实际发布动态时只用了两个，那么另外一个图需要设置成-2）

	var extra any
	if extraBytes, mErr := json.Marshal(&UserGenFeedMediaExtra{
		DeviceId: feedMedia.DeviceId,
	}); mErr == nil {
		extra = string(extraBytes)
	}

	insertFeedQuery := `
		INSERT INTO user_gen_medias (media_id, qid, scenario, media_object_key, file_size, status, created_at, extra)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`
	_, err := db.Exec(insertFeedQuery,
		feedMedia.MediaId,
		qid,
		scenario,
		feedMedia.ObjectKey,
		feedMedia.FileSize,
		status,
		now,
		extra,
	)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":       qid,
			"mediaId":   feedMedia.MediaId,
			"objectKey": feedMedia.ObjectKey,
			"deviceId":  feedMedia.DeviceId,
		}).Error("saveUploadFeedMediaInfo 插入用户生成的媒体失败")
		return err
	}
	return nil
}
