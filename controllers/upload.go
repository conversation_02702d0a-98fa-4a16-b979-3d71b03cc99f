package controllers

import (
	"fmt"
	"qing-feeds/database"
	"qing-feeds/s4"
	"qing-feeds/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"github.com/sirupsen/logrus"
)

const MaxAvatarDataSize = 10 * 1024 * 1024 // 10M
const MinAvatarWidth = 1000
const MinAvatarHeight = 1000

type GetMediaUploadURLRequest struct {
	FileType string `json:"file_type"`
	FileSize int64  `json:"file_size"`
	Width    int64  `json:"width"`
	Height   int64  `json:"height"`
}
type GetMediaUploadURLResponse struct {
	UploadURL string `json:"upload_url"`
	MediaId   string `json:"media_id"`
}

// GetMediaUploadURL 处理 GET /api/feeds/upload-media-url
// 获取上传头像的上传地址 限制：上限：最大不能超过10M，下限：最小不能低于像素1000x1000
func (f *FeedController) GetMediaUploadURL(ctx iris.Context) {
	// 从中间件获取用户ID（由CheckAuthorization中间件处理JWT后设置）
	qid := ctx.Values().GetString("x-qid")
	deviceID := ctx.Values().GetString("x-device-id")

	var req GetMediaUploadURLRequest
	req.FileType = strings.TrimSpace(ctx.URLParam("file_type"))
	req.FileSize, _ = ctx.URLParamInt64("file_size")
	req.Width, _ = ctx.URLParamInt64("width")
	req.Height, _ = ctx.URLParamInt64("height")

	if req.FileSize <= 0 {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Debug("GetMediaUploadURL FileSize 无效")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 最大不能超多10M
	if req.FileSize > MaxAvatarDataSize {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Debug("GetMediaUploadURL FileSize 图片大小超过限制")
		utils.InvalidRequestError(ctx, "图片大小超过限制")
		return
	}

	// 最小像素
	if req.Width < MinAvatarWidth || req.Height < MinAvatarHeight {
		logrus.WithFields(logrus.Fields{
			"qid":       qid,
			"file_type": req.FileType,
			"file_size": req.FileSize,
			"width":     req.Width,
			"height":    req.Height,
		}).Debug("GetMediaUploadURL Width Height 图片尺寸过小")
		utils.InvalidRequestError(ctx, "图片尺寸过小")
		return
	}

	// 获取用户资料
	userinfo, err := findUserByQid(qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid": qid,
		}).Error("GetMediaUploadURL findUserByQid 获取用户资料失败")

		utils.InternalError(ctx, "")
		return
	}

	if userinfo == nil {
		logrus.WithFields(logrus.Fields{
			"qid": qid,
		}).Error("GetMediaUploadURL findUserByQid 用户不存在")

		utils.InvalidRequestError(ctx, "")
		return
	}

	// 生成一个随机的avatar_id
	suffix := f.getImageTypeSuffix(req.FileType)
	mediaId, objectKey := generateMediaInfo(req.FileType)
	if suffix != "" {
		objectKey = fmt.Sprintf("%v.%v", objectKey, suffix)
	}

	// 获取上传地址
	uploadUrl := s4.GetUploadUrl(objectKey, req.FileSize)

	// 保存对应关系到Redis
	err = f.saveMediaUploadInfo(qid, mediaId, deviceID, objectKey)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"mediaId": mediaId,
		}).Error("GetMediaUploadURL saveMediaUploadInfo 失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"qid":       qid,
		"file_type": req.FileType,
		"file_size": req.FileSize,
		"width":     req.Width,
		"height":    req.Height,
		"uploadUrl": uploadUrl,
		"mediaId":   mediaId,
	}).Debug("获取动态媒体文件的上传URL成功")

	response := &GetMediaUploadURLResponse{
		UploadURL: uploadUrl,
		MediaId:   mediaId,
	}

	// 返回成功响应
	utils.SuccessWithMsg(ctx, response, "")
}

// getImageTypeSuffix 获取文件的格式后缀 png jpg等
func (f *FeedController) getImageTypeSuffix(imageType string) string {
	if imageType == "" {
		return ""
	}
	// 比如 image/png 返回png, 如果是png直接返回png
	if strings.Contains(imageType, "/") {
		value := imageType[strings.Index(imageType, "/")+1:]
		return strings.TrimSpace(strings.Trim(value, "."))
	}
	return strings.TrimSpace(strings.Trim(imageType, "."))
}

// generateMediaInfo 生成文件的信息
func generateMediaInfo(fileType string) (string, string) {
	randomId := ""
	for i := 0; i < 10; i++ {
		id, err := gonanoid.Generate("0123456789abcdefghijklmnopqrstuvwxyz", 14)
		if err == nil {
			randomId = id
			break
		}
	}

	if randomId == "" {
		randomId = utils.GetRandomString(14)
	}

	ms := time.Now().UnixMilli()

	if utils.IsImageType(fileType) {
		return randomId, fmt.Sprintf("feed/img-%v-%v", ms, randomId)
	} else if utils.IsVideoType(fileType) {
		return randomId, fmt.Sprintf("feed/vid-%v-%v", ms, randomId)
	} else {
		return randomId, fmt.Sprintf("feed/file-%v-%v", ms, randomId)
	}
}

// saveMediaUploadInfo 保存上传信息
func (f *FeedController) saveMediaUploadInfo(qid, avatarId, deviceId string, objectKey string) error {
	key := fmt.Sprintf("avatar:upload:%v:%v:%v", qid, avatarId, deviceId)
	if err := database.SetCache(key, objectKey, 30*time.Minute); err != nil {
		return err
	}
	return nil
}
