package controllers

import (
	"database/sql"
	"fmt"
	"qing-feeds/database"
	"qing-feeds/models"
	"strings"
	"time"
)

// validateCreateFeedGroupRequest 验证创建用户组请求
func (fg *FeedGroupController) validateCreateFeedGroupRequest(req *models.CreateFeedGroupRequest) error {
	if strings.TrimSpace(req.GroupName) == "" {
		return fmt.Errorf("分组名称不能为空")
	}

	if req.GroupType < models.GroupTypeTemporary || req.GroupType > models.GroupTypePermanent {
		return fmt.Errorf("无效的分组类型")
	}

	// 验证成员列表中的QID格式
	for i, member := range req.Members {
		if strings.TrimSpace(member) == "" {
			return fmt.Errorf("第%d个成员QID不能为空", i+1)
		}
	}

	return nil
}

// insertFeedGroup 插入用户组记录
func (fg *FeedGroupController) insertFeedGroup(group *models.FeedGroup) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		INSERT INTO user_feed_groups (group_id, group_name, owner_qid, group_type, created_at, update_at, status)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
	`

	_, err := db.Exec(query,
		group.GroupID,
		group.GroupName,
		group.OwnerQid,
		group.GroupType,
		group.CreatedAt,
		group.UpdateAt,
		group.Status,
	)

	return err
}

// getFeedGroupByID 根据ID查询用户组
func (fg *FeedGroupController) getFeedGroupByID(groupID string) (*models.FeedGroup, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT group_id, group_name, owner_qid, group_type, created_at, update_at, status
		FROM user_feed_groups
		WHERE group_id = $1
	`

	group := &models.FeedGroup{}
	err := db.QueryRow(query, groupID).Scan(
		&group.GroupID,
		&group.GroupName,
		&group.OwnerQid,
		&group.GroupType,
		&group.CreatedAt,
		&group.UpdateAt,
		&group.Status,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return group, nil
}

// softDeleteFeedGroup 软删除用户组
func (fg *FeedGroupController) softDeleteFeedGroup(groupID string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	now := time.Now().UnixMilli()
	query := `UPDATE user_feed_groups SET status = 1, update_at = $1 WHERE group_id = $2`
	_, err := db.Exec(query, now, groupID)
	return err
}

// updateFeedGroupName 更新用户组名称
func (fg *FeedGroupController) updateFeedGroupName(groupID, groupName string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	now := time.Now().UnixMilli()
	query := `UPDATE user_feed_groups SET group_name = $1, update_at = $2 WHERE group_id = $3`
	_, err := db.Exec(query, groupName, now, groupID)
	return err
}

// addMembersToGroup 添加成员到用户组
func (fg *FeedGroupController) addMembersToGroup(groupID, ownerQid string, members []string, createdAt int64) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	// 开始事务
	tx, err := db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 先删除已存在的成员记录（避免重复）
	deleteQuery := `DELETE FROM user_feed_group_members WHERE group_id = $1 AND owner_qid = $2 AND member_qid = ANY($3)`
	_, err = tx.Exec(deleteQuery, groupID, ownerQid, fmt.Sprintf("{%s}", strings.Join(members, ",")))
	if err != nil {
		return err
	}

	// 插入新的成员记录
	insertQuery := `
		INSERT INTO user_feed_group_members (group_id, owner_qid, member_qid, created_at)
		VALUES ($1, $2, $3, $4)
	`

	for _, member := range members {
		_, err = tx.Exec(insertQuery, groupID, ownerQid, member, createdAt)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// removeMembersFromGroup 从用户组中移除成员
func (fg *FeedGroupController) removeMembersFromGroup(groupID, ownerQid string, members []string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `DELETE FROM user_feed_group_members WHERE group_id = $1 AND owner_qid = $2 AND member_qid = ANY($3)`
	_, err := db.Exec(query, groupID, ownerQid, fmt.Sprintf("{%s}", strings.Join(members, ",")))
	return err
}

// getFeedGroupsByOwner 获取用户的分组列表
func (fg *FeedGroupController) getFeedGroupsByOwner(ownerQid string) ([]models.FeedGroupResponse, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT group_id, group_name, group_type, created_at, update_at
		FROM user_feed_groups
		WHERE owner_qid = $1 AND status = 0
		ORDER BY created_at DESC
	`

	rows, err := db.Query(query, ownerQid)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var groups []models.FeedGroupResponse
	for rows.Next() {
		var group models.FeedGroupResponse
		err := rows.Scan(
			&group.GroupID,
			&group.GroupName,
			&group.GroupType,
			&group.CreatedAt,
			&group.UpdateAt,
		)
		if err != nil {
			return nil, err
		}

		// 查询成员列表
		members, err := fg.getFeedGroupMembers(group.GroupID, ownerQid)
		if err != nil {
			return nil, err
		}

		group.Members = members
		group.MemberCount = len(members)

		groups = append(groups, group)
	}

	return groups, nil
}

// getFeedGroupMembers 获取用户组成员列表
func (fg *FeedGroupController) getFeedGroupMembers(groupID, ownerQid string) ([]string, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT member_qid
		FROM user_feed_group_members
		WHERE group_id = $1 AND owner_qid = $2
		ORDER BY created_at ASC
	`

	rows, err := db.Query(query, groupID, ownerQid)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var members []string
	for rows.Next() {
		var member string
		err := rows.Scan(&member)
		if err != nil {
			return nil, err
		}
		members = append(members, member)
	}

	return members, nil
}
