package controllers

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"qing-feeds/database"
	"qing-feeds/models"
	"strings"

	"github.com/sirupsen/logrus"
)

// insertFeed 插入动态记录
func (f *FeedController) insertFeed(tx *sql.Tx, feed *models.FeedDetailDBItem) error {
	query := `
		INSERT INTO feeds (feed_id, feed_qid, content, vis_type, location, created_at, update_at, status)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`

	_, err := tx.Exec(query,
		feed.FeedID,
		feed.FeedQid,
		feed.Content,
		feed.VisType,
		feed.Location,
		feed.CreatedAt,
		feed.UpdateAt,
		feed.Status,
	)

	return err
}

// insertFeedMedias 插入媒体记录
func (f *FeedController) insertFeedMedias(tx *sql.Tx, feedID string, feedQid string, mediaList []FeedMediaRequest, createdAt int64) error {
	query := `
		INSERT INTO feed_medias (
			media_id, feed_id, feed_qid, media_type, media_order, media_object_key,
			thumbnail_object_key, thumbhash_hash, file_size, duration, width, height,
			created_at, exif, extra
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
	`

	feedIDStr := fmt.Sprintf("%d", feedID)

	for _, media := range mediaList {
		var exifJSON, extraJSON []byte
		var err error

		if media.Exif != nil {
			exifJSON, err = json.Marshal(media.Exif)
			if err != nil {
				return fmt.Errorf("序列化EXIF信息失败: %w", err)
			}
		}

		if media.Extra != nil {
			extraJSON, err = json.Marshal(media.Extra)
			if err != nil {
				return fmt.Errorf("序列化扩展信息失败: %w", err)
			}
		}

		var duration sql.NullInt32
		if media.Duration != nil {
			duration = sql.NullInt32{Int32: int32(*media.Duration), Valid: true}
		}

		_, err = tx.Exec(query,
			media.MediaID,
			feedIDStr,
			feedQid,
			media.MediaType,
			media.MediaOrder,
			media.MediaObjectKey,
			media.ThumbnailObjectKey,
			media.ThumbHash,
			media.FileSize,
			duration,
			media.Width,
			media.Height,
			createdAt,
			exifJSON,
			extraJSON,
		)

		if err != nil {
			return err
		}
	}

	return nil
}

// getFeedByID 根据ID查询动态
func (f *FeedController) getFeedByID(feedID string) (*models.FeedDetailDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT feed_id, feed_qid, content, vis_type, public, location, created_at, update_at, status, extra
		FROM feeds
		WHERE feed_id = $1
	`

	feed := &models.FeedDetailDBItem{}
	err := db.QueryRow(query, feedID).Scan(
		&feed.FeedID,
		&feed.FeedQid,
		&feed.Content,
		&feed.VisType,
		&feed.Public,
		&feed.Location,
		&feed.CreatedAt,
		&feed.UpdateAt,
		&feed.Status,
		&feed.Extra,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	return feed, nil
}

// getFeedWithMediasByID 根据ID查询动态(带上媒体列表)
func (f *FeedController) getFeedWithMediasByID(feedID string) (*models.FeedDetailWithMediasDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT f.feed_id, f.feed_qid, f.content, f.vis_type, f.public, f.location, f.created_at, f.update_at, f.status, f.extra,
		COALESCE(
        	jsonb_agg(
        		FILTER (WHERE fm.media_id IS NOT NULL)
            	jsonb_build_object(
					'media_id', fm.media_id,
        		    'feed_id', fm.feed_id,
        		    'feed_qid', fm.feed_qid,
                	'media_type', fm.media_type,
                	'media_order', fm.media_order,
                	'media_object_key', fm.media_object_key,
        		    'created_at', fm.created_at,
                	'thumbhash', fm.thumbhash,
                	'file_size', fm.file_size,
                	'duration', fm.duration,
                	'width', fm.width,
                	'height', fm.height,
        		    'exif', fm.exif,
        		    'extra', fm.extra
            	)
            	ORDER BY fm.media_order ASC
			),
			'[]'::jsonb
		) AS medias
		FROM feeds AS f
		LEFT JOIN feed_medias AS fm ON f.feed_id = fm.feed_id
		WHERE f.feed_id = $1
		GROUP BY f.feed_id;
	`

	feed := &models.FeedDetailWithMediasDBItem{
		Medias: make([]models.FeedMediaDBItem, 0),
	}
	var mediasJSON []byte // **关键点**: 用于临时接收 JSONB 数据的字节切片

	err := db.QueryRow(query, feedID).Scan(
		&feed.FeedID,
		&feed.FeedQid,
		&feed.Content,
		&feed.VisType,
		&feed.Public,
		&feed.Location,
		&feed.CreatedAt,
		&feed.UpdateAt,
		&feed.Status,
		&feed.Extra,
		&mediasJSON, // 将 medias 列扫描到字节切片中
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	if len(mediasJSON) > 0 {
		// **关键点**: 将 JSON 字节反序列化到 feed.Medias 结构体切片中
		if mErr := json.Unmarshal(mediasJSON, &feed.Medias); mErr != nil {
			logrus.WithError(mErr).Error("getFeedWithMediasByID 反序列化媒体 media JSON 时出错")
		}
	}

	return feed, nil
}

// updateFeed 更新动态
func (f *FeedController) updateFeed(tx *sql.Tx, feedID string, req *UpdateFeedRequest, updateAt int64) error {
	setParts := []string{"update_at = $1"}
	args := []interface{}{updateAt}
	argIndex := 2

	if req.Content != nil {
		setParts = append(setParts, fmt.Sprintf("content = $%d", argIndex))
		args = append(args, *req.Content)
		argIndex++
	}

	if req.VisType != nil {
		setParts = append(setParts, fmt.Sprintf("vis_type = $%d", argIndex))
		args = append(args, *req.VisType)
		argIndex++
	}

	if req.Location != nil {
		locationJSON, err := json.Marshal(*req.Location)
		if err != nil {
			return fmt.Errorf("序列化位置信息失败: %w", err)
		}
		setParts = append(setParts, fmt.Sprintf("location = $%d", argIndex))
		args = append(args, locationJSON)
		argIndex++
	}

	query := fmt.Sprintf("UPDATE feeds SET %s WHERE feed_id = $%d", strings.Join(setParts, ", "), argIndex)
	args = append(args, feedID)

	_, err := tx.Exec(query, args...)
	return err
}

// deleteFeedVisibilities 删除可见性记录
func (f *FeedController) deleteFeedVisibilities(tx *sql.Tx, feedID string) error {
	query := `DELETE FROM feed_visibilities WHERE feed_id = $1`
	_, err := tx.Exec(query, fmt.Sprintf("%d", feedID))
	return err
}

// deleteFeedMedias 删除媒体记录
func (f *FeedController) deleteFeedMedias(tx *sql.Tx, feedID string) error {
	query := `DELETE FROM feed_medias WHERE feed_id = $1`
	_, err := tx.Exec(query, fmt.Sprintf("%d", feedID))
	return err
}

// softDeleteFeed 软删除动态
func (f *FeedController) softDeleteFeed(feedID string) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `UPDATE feeds SET status = $1 WHERE feed_id = $`
	_, err := db.Exec(query, FeedStatusDeleted, feedID)
	return err
}

// getUserVisibleFeeds 获取用户可见的动态列表
func (f *FeedController) getUserVisibleFeeds(qid string, belowCreatedAt int64, limit int64) ([]models.FeedDetailWithMediasDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	var rows *sql.Rows
	var err error

	if belowCreatedAt > 0 {
		query := `
		SELECT f.feed_id, f.feed_qid, f.content, f.vis_type, f.public, f.location, f.created_at, f.update_at, f.status, f.extra,
		COALESCE(
        	jsonb_agg(
        		FILTER (WHERE fm.media_id IS NOT NULL)
            	jsonb_build_object(
					'media_id', fm.media_id,
        		    'feed_id', fm.feed_id,
        		    'feed_qid', fm.feed_qid,
                	'media_type', fm.media_type,
                	'media_order', fm.media_order,
                	'media_object_key', fm.media_object_key,
        		    'created_at', fm.created_at,
                	'thumbhash', fm.thumbhash,
                	'file_size', fm.file_size,
                	'duration', fm.duration,
                	'width', fm.width,
                	'height', fm.height,
        		    'exif', fm.exif,
        		    'extra', fm.extra
            	)
            	ORDER BY fm.media_order ASC
			),
			'[]'::jsonb
		) AS medias
		FROM user_visible_feeds AS uvf
		JOIN feeds AS f ON uvf.feed_id = f.feed_id
		LEFT JOIN feed_medias AS fm ON f.feed_id = fm.feed_id
		WHERE uvf.qid = $1 AND uvf.created_at < $2
		ORDER BY uvf.created_at DESC
		LIMIT $3
	`
		rows, err = db.Query(query, qid, belowCreatedAt, limit)
	} else {
		query := `
		SELECT f.feed_id, f.feed_qid, f.content, f.vis_type, f.public, f.location, f.created_at, f.update_at, f.status, f.extra,
		COALESCE(
        	jsonb_agg(
        		FILTER (WHERE fm.media_id IS NOT NULL)
            	jsonb_build_object(
                	'media_id', fm.media_id,
        		    'feed_id', fm.feed_id,
        		    'feed_qid', fm.feed_qid,
                	'media_type', fm.media_type,
                	'media_order', fm.media_order,
                	'media_object_key', fm.media_object_key,
        		    'created_at', fm.created_at,
                	'thumbhash', fm.thumbhash,
                	'file_size', fm.file_size,
                	'duration', fm.duration,
                	'width', fm.width,
                	'height', fm.height,
        		    'exif', fm.exif,
        		    'extra', fm.extra
            	)
            	ORDER BY fm.media_order ASC
			),
			'[]'::jsonb
		) AS medias
		FROM user_visible_feeds AS uvf
		JOIN feeds AS f ON uvf.feed_id = f.feed_id
		LEFT JOIN feed_medias AS fm ON f.feed_id = fm.feed_id
		WHERE uvf.qid = $1
		ORDER BY uvf.created_at DESC
		LIMIT $2
	`
		rows, err = db.Query(query, qid, limit)
	}

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	defer rows.Close()

	feedList := make([]models.FeedDetailWithMediasDBItem, 0)
	for rows.Next() {
		var mediasJSON []byte // **关键点**: 用于临时接收 JSONB 数据的字节切片

		feed := models.FeedDetailWithMediasDBItem{
			Medias: make([]models.FeedMediaDBItem, 0),
		}

		if sErr := rows.Scan(
			&feed.FeedID,
			&feed.FeedQid,
			&feed.Content,
			&feed.VisType,
			&feed.Public,
			&feed.Location,
			&feed.CreatedAt,
			&feed.UpdateAt,
			&feed.Status,
			&feed.Extra,
			&mediasJSON, // 将 medias 列扫描到字节切片中
		); sErr != nil {
			logrus.WithError(sErr).Error("getUserVisibleFeeds 扫描动态信息失败")
			continue
		}

		if len(mediasJSON) > 0 {
			// **关键点**: 将 JSON 字节反序列化到 feed.Medias 结构体切片中
			if mErr := json.Unmarshal(mediasJSON, &feed.Medias); mErr != nil {
				logrus.WithError(mErr).Error("getUserVisibleFeeds 反序列化媒体 media JSON 时出错")
			}
		}

		feedList = append(feedList, feed)
	}

	return feedList, nil
}

// getUserFeedsVisibleTo 获取用户B的动态中用户A可见的
func (f *FeedController) getUserFeedsVisibleTo(targetQid, viewerQid string, belowCreatedAt int64, limit int64) ([]models.FeedDetailWithMediasDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	// 查询动态列表
	var rows *sql.Rows
	var err error

	if belowCreatedAt > 0 {
		query := `
		SELECT f.feed_id, f.feed_qid, f.content, f.vis_type, f.public, f.location, f.created_at, f.update_at, f.status, f.extra,
		COALESCE(
        	jsonb_agg(
        		FILTER (WHERE fm.media_id IS NOT NULL)
            	jsonb_build_object(
					'media_id', fm.media_id,
        		    'feed_id', fm.feed_id,
        		    'feed_qid', fm.feed_qid,
                	'media_type', fm.media_type,
                	'media_order', fm.media_order,
                	'media_object_key', fm.media_object_key,
        		    'created_at', fm.created_at,
                	'thumbhash', fm.thumbhash,
                	'file_size', fm.file_size,
                	'duration', fm.duration,
                	'width', fm.width,
                	'height', fm.height,
        		    'exif', fm.exif,
        		    'extra', fm.extra
            	)
            	ORDER BY fm.media_order ASC
			),
			'[]'::jsonb
		) AS medias
		FROM user_visible_feeds AS uvf
		JOIN feeds AS f ON uvf.feed_id = f.feed_id
		LEFT JOIN feed_medias AS fm ON f.feed_id = fm.feed_id
		WHERE uvf.qid = $1 AND uvf.feed_qid = $2 AND uvf.created_at < $3
		ORDER BY uvf.created_at DESC
		LIMIT $4
	`
		rows, err = db.Query(query, viewerQid, targetQid, belowCreatedAt, limit)
	} else {
		query := `
		SELECT f.feed_id, f.feed_qid, f.content, f.vis_type, f.public, f.location, f.created_at, f.update_at, f.status, f.extra,
		COALESCE(
        	jsonb_agg(
        		FILTER (WHERE fm.media_id IS NOT NULL)
            	jsonb_build_object(
                	'media_id', fm.media_id,
        		    'feed_id', fm.feed_id,
        		    'feed_qid', fm.feed_qid,
                	'media_type', fm.media_type,
                	'media_order', fm.media_order,
                	'media_object_key', fm.media_object_key,
        		    'created_at', fm.created_at,
                	'thumbhash', fm.thumbhash,
                	'file_size', fm.file_size,
                	'duration', fm.duration,
                	'width', fm.width,
                	'height', fm.height,
        		    'exif', fm.exif,
        		    'extra', fm.extra
            	)
            	ORDER BY fm.media_order ASC
			),
			'[]'::jsonb
		) AS medias
		FROM user_visible_feeds AS uvf
		JOIN feeds AS f ON uvf.feed_id = f.feed_id
		LEFT JOIN feed_medias AS fm ON f.feed_id = fm.feed_id
		WHERE uvf.qid = $1 AND uvf.feed_qid = $2
		ORDER BY uvf.created_at DESC
		LIMIT $3
	`
		rows, err = db.Query(query, viewerQid, targetQid, limit)
	}

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	defer rows.Close()

	feedList := make([]models.FeedDetailWithMediasDBItem, 0)
	for rows.Next() {
		var mediasJSON []byte // **关键点**: 用于临时接收 JSONB 数据的字节切片

		feed := models.FeedDetailWithMediasDBItem{
			Medias: make([]models.FeedMediaDBItem, 0),
		}

		if sErr := rows.Scan(
			&feed.FeedID,
			&feed.FeedQid,
			&feed.Content,
			&feed.VisType,
			&feed.Public,
			&feed.Location,
			&feed.CreatedAt,
			&feed.UpdateAt,
			&feed.Status,
			&feed.Extra,
			&mediasJSON, // 将 medias 列扫描到字节切片中
		); sErr != nil {
			logrus.WithError(sErr).Error("getUserVisibleFeeds 扫描动态信息失败")
			continue
		}

		if len(mediasJSON) > 0 {
			// **关键点**: 将 JSON 字节反序列化到 feed.Medias 结构体切片中
			if mErr := json.Unmarshal(mediasJSON, &feed.Medias); mErr != nil {
				logrus.WithError(mErr).Error("getUserVisibleFeeds 反序列化媒体 media JSON 时出错")
			}
		}

		feedList = append(feedList, feed)
	}

	return feedList, nil
}

// getFeedMedias 获取动态的媒体列表
func (f *FeedController) getFeedMedias(feedID string) ([]models.FeedMediaDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT media_id, feed_id, feed_qid, media_type, media_order, media_object_key, created_at, thumbhash, file_size, duration, width, height, exif, extra
		FROM feed_medias
		WHERE feed_id = $1
		ORDER BY media_order ASC
	`

	rows, err := db.Query(query, feedID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil // 不存在
		}
		return nil, err
	}
	defer rows.Close()

	var mediaList []models.FeedMediaDBItem
	for rows.Next() {
		var media models.FeedMediaDBItem

		if sErr := rows.Scan(
			&media.MediaID,
			&media.FeedID,
			&media.FeedQid,
			&media.MediaType,
			&media.MediaOrder,
			&media.MediaObjectKey,
			&media.CreatedAt,
			&media.ThumbHash,
			&media.FileSize,
			&media.Duration,
			&media.Width,
			&media.Height,
			&media.Exif,
			&media.Extra,
		); sErr != nil {
			logrus.WithError(sErr).WithFields(logrus.Fields{
				"feed_id": feedID,
			}).Error("getFeedMedias 扫描媒体信息失败")
			continue
		}

		mediaList = append(mediaList, media)
	}

	return mediaList, nil
}

// isFriend 检查两个用户是否是好友
func (f *FeedController) isFriend(qid, targetQid string) (bool, error) {
	// TODO: 实现好友关系检查
	return true, nil
}
