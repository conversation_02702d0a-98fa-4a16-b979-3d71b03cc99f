package controllers

import (
	"encoding/json"
	"fmt"
	"qing-feeds/utils"
	"strconv"
	"strings"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

const MomentsFeedPageCount = 20 // 一次请求返回多少条动态
const DBFeedPageCount = 40      // 每次从数据库查询多少条动态

// FeedController 处理动态相关的端点
type FeedController struct{}

// NewFeedController 创建新的动态控制器
func NewFeedController() *FeedController {
	return &FeedController{}
}

type FeedLocation struct {
	LocationId   string    `json:"location_id,omitempty"`
	LocationName string    `json:"location_name,omitempty"`
	Coordinates  []float64 `json:"coordinates,omitempty"`
}

func (fl FeedLocation) IsEmpty() bool {
	return len(fl.Coordinates) != 2
}

type MomentsFeed struct {
	MomentsFeedDetail
	Likes    []MomentsFeedDetailLike    `json:"likes"`
	Comments []MomentsFeedDetailComment `json:"comments"`
}

type MomentsFeedOwnerInfo struct {
	Qid          string `json:"qid,omitempty"`
	Avatar       string `json:"avatar,omitempty"`
	Username     string `json:"username,omitempty"`
	UsernameNote string `json:"username_note,omitempty"`
}

// MomentsCoverConfig 好友圈封面设置
type MomentsCoverConfig struct {
	MediaType string `json:"media_type,omitempty"` // image video
	MediaUrl  string `json:"media_url,omitempty"`
}
type MomentsFeedsResponse struct {
	List        []*MomentsFeed       `json:"list"`
	CoverConfig MomentsCoverConfig   `json:"cover_config,omitempty"` // 封面配置
	OwnerInfo   MomentsFeedOwnerInfo `json:"owner_info,omitempty"`
	NextCursor  string               `json:"next_cursor"`
}

// GetMomentsFeeds 处理 GET /api/feeds/moments
// 获取用户A私域动态列表
func (f *FeedController) GetMomentsFeeds(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	cursor := strings.TrimSpace(ctx.URLParam("cursor")) // 获取翻页标识

	// 模拟假数据
	if qid != 10050 {
		f.fakeMomentsFeeds(ctx)
		return
	}

	ok, belowCreatedAt := toValidFeedCursor(cursor)
	if ok == false {
		logrus.WithFields(logrus.Fields{
			"qid":    qid,
			"cursor": cursor,
		}).Error("GetMomentsFeeds 无效的翻页标识")
		belowCreatedAt = 0 // 重置
	}

	// 从用户可见动态表查询 每次返回20条，但请求40条，目的是为了防止需要过滤时，额外还要查询数据库的情况(比如根据可见分级未审核的动态需要过滤的情况)
	feeds, err := f.getUserVisibleFeeds(qid, belowCreatedAt, DBFeedPageCount)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"cursor": cursor,
		}).Error("GetMomentsFeeds 查询用户可见动态失败")
		utils.InternalError(ctx, "")
		return
	}

	feedIDs := make([]int64, 0)
	feedMediaIds := make([]int64, 0)
	for _, feed := range feeds {
		feedIDs = append(feedIDs, feed.FeedID)
		for _, media := range feed.Medias {
			feedMediaIds = append(feedMediaIds, media.MediaID)
		}
	}

	feedsCommentsMap, err := f.getMultiMomentsFeedComments(qid, feedIDs)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"feedIDs": feedIDs,
		}).Error("GetMomentsFeeds getMultiMomentsFeedComments 查询动态评论失败")
		utils.InternalError(ctx, "")
		return
	}

	feedsLikesMap, err := f.getMultiMomentsFeedLikes(qid, feedIDs)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":     qid,
			"feedIDs": feedIDs,
		}).Error("GetMomentsFeeds getMultiMomentsFeedLikes 查询动态点赞失败")
		utils.InternalError(ctx, "")
		return
	}

	feedMediasMap, err := f.getMultiFeedMediasBasicInfo(feedMediaIds)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":          qid,
			"feedMediaIds": feedMediaIds,
		}).Error("GetMomentsFeeds getMultiFeedMediasBasicInfo 查询动态媒体失败")
		utils.InternalError(ctx, "")
		return
	}

	nextCursor := ""
	list := make([]*MomentsFeed, 0)

	for _, feed := range feeds {
		// TODO 过滤掉关系不对的用户的动态，过滤掉状态不对的动态，过滤可见分级不符的动态

		// 处理位置信息
		var location FeedLocation
		if feed.Location.Valid {
			_ = json.Unmarshal([]byte(feed.Location.String), &location)
		}

		feedUserAvatar := "https://qing-test-files.bilifans.com/fake/1.JPG" // 从redis获取缓存
		feedUsername := "小明"                                                // 从redis获取缓存
		liked := false                                                      // 自己是否已点赞
		likeCount := int64(0)                                               // 总点赞数(包含公域 + 私域)

		// 获取好友点赞和评论
		comments := make([]MomentsFeedDetailComment, 0) // 所有好友评论列表
		if cms, exist := feedsCommentsMap[feed.FeedID]; exist {
			for _, comment := range cms {
				commentIDStr, _ := utils.EncodeFeedCommentID(comment.CommentID)
				qidStr, _ := utils.EncodeQID(comment.Qid)

				// 回复的评论ID和回复的用户ID可能为空
				replyToCommentIDStr := ""
				replyToQidStr := ""
				if comment.ReplyToCommentID.Valid {
					replyToCommentIDStr, _ = utils.EncodeFeedCommentID(comment.ReplyToCommentID.Int64)
				}
				if comment.ReplyToQid.Valid {
					replyToQidStr, _ = utils.EncodeQID(comment.ReplyToQid.Int64)
				}

				comments = append(comments, MomentsFeedDetailComment{
					CommentID:           commentIDStr,
					Qid:                 qidStr,
					Username:            "小明",                                              // TODO 从redis获取
					UsernameNote:        "小明明",                                             // TODO 从redis获取
					Avatar:              "https://qing-test-files.bilifans.com/fake/1.JPG", // TODO 从redis获取
					Content:             comment.Content,
					CreatedAt:           comment.CreatedAt,
					ReplyToCommentID:    replyToCommentIDStr,
					ReplyToQid:          replyToQidStr,
					ReplyToUsername:     "", // TODO 从redis获取
					ReplyToUsernameNote: "", // TODO 从redis获取
				})
			}
		}

		likes := make([]MomentsFeedDetailLike, 0) // 所有好友点赞列表
		if lks, exist := feedsLikesMap[feed.FeedID]; exist {
			for _, like := range lks {
				qidStr, _ := utils.EncodeQID(like.Qid)
				likes = append(likes, MomentsFeedDetailLike{
					Qid:      qidStr,
					Avatar:   "", // TODO 从redis获取
					Username: "", // TODO 从redis获取
				})
			}
		}

		// 处理媒体信息
		images, videos := splitMedias(feedMediasMap, feed.Medias)

		// 编码 feed_id feed_qid
		feedIDStr, _ := utils.EncodeFeedID(feed.FeedID)
		feedQidStr, _ := utils.EncodeQID(feed.FeedQid)
		detail := MomentsFeedDetail{
			FeedID:         feedIDStr,
			FeedQid:        feedQidStr,
			FeedUserAvatar: feedUserAvatar,
			FeedUsername:   feedUsername,
			Content:        feed.Content,
			VisType:        f.intVisTypeToString(feed.VisType),
			Public:         feed.Public,
			Location:       location,
			CreatedAt:      feed.CreatedAt / 1000,
			UpdateAt:       feed.UpdateAt,
			Liked:          liked,
			LikeCount:      likeCount,
			Images:         images,
			Videos:         videos,
			PinnedFeedID:   "xxx",
		}
		list = append(list, &MomentsFeed{
			MomentsFeedDetail: detail,
			Likes:             likes,
			Comments:          comments,
		})

		nextCursor = fmt.Sprintf("t:%d", feed.CreatedAt)
		if len(list) >= 20 {
			break
		}
	}

	// 从数据库中获取不足一页，说明没有更多数据了
	if len(feeds) < DBFeedPageCount {
		nextCursor = ""
	}

	// 如果是首页，返回封面配置
	var coverConfig MomentsCoverConfig
	if cursor == "" {
		coverConfig = MomentsCoverConfig{
			MediaType: "image",
			MediaUrl:  "https://qing-test-files.bilifans.com/moments/moments_cover.jpg",
		}
	}
	response := &MomentsFeedsResponse{
		List:        list,
		CoverConfig: coverConfig,
		NextCursor:  nextCursor,
	}

	utils.SuccessWithMsg(ctx, response, "")
}

// 如果cursor是按照时间戳来的，那么以 t: 开头(t:1754023695000), 后续如果更精精准，可以以 feed_id和时间组合的 以 ft:feed_1836496DGCSGVG:1754023695000 格式
func toValidFeedCursor(cursor string) (bool, int64) {
	if cursor == "" {
		return true, 0
	}

	if strings.HasPrefix(cursor, "t:") && len(cursor) == 15 {
		ms, err := strconv.ParseInt(cursor[2:], 10, 64)
		if err != nil {
			return false, 0
		}
		// 小于20250801也不对
		if ms <= 1753977600000 {
			return false, 0
		}
		return true, ms
	}
	return false, 0
}

type GetUserMomentsFeedsResponse struct {
	List        []DayFeeds           `json:"list"`
	TopList     []*MomentsFeed       `json:"top_list,omitempty"`
	CoverConfig MomentsCoverConfig   `json:"cover_config,omitempty"` // 封面配置
	OwnerInfo   MomentsFeedOwnerInfo `json:"owner_info,omitempty"`
	NextCursor  string               `json:"next_cursor"`
}
type DayFeeds struct {
	CreatedAt int64          `json:"created_at"` // 秒
	Feeds     []*MomentsFeed `json:"feeds"`
}

// GetUserMomentsFeeds 处理 GET /api/feeds/:qid/moments
// 获取用户A查看用户B的所有可见的动态
func (f *FeedController) GetUserMomentsFeeds(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")
	targetQidStr := strings.TrimSpace(ctx.Params().Get("target_qid"))
	cursor := strings.TrimSpace(ctx.URLParam("cursor"))

	if targetQidStr == "" {
		logrus.WithFields(logrus.Fields{
			"qid":        qid,
			"target_qid": targetQidStr,
		}).Error("GetUserMomentsFeeds 无效的用户ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 模拟假数据
	if qid > 0 {
		f.fakeSomeoneMomentsFeeds(ctx)
		return
	}

	targetQid, err := utils.DecodeQID(targetQidStr)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":        qid,
			"target_qid": targetQidStr,
		}).Error("GetUserMomentsFeeds 无效的用户ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	ok, belowCreatedAt := toValidFeedCursor(cursor)
	if ok == false {
		logrus.WithFields(logrus.Fields{
			"qid":    qid,
			"cursor": cursor,
		}).Error("GetUserMomentsFeeds 无效的翻页标识")
		belowCreatedAt = 0 // 重置
	}

	// TODO 获取两个人的关系，陌生人？好友？拉黑？（单项拉黑，双向拉黑）
	// TODO 如果时好友关系可以查看，如果是陌生人，需要根据targetQid的配置来判断是否可以查看，如果可以看还要看targetQid有没有拉黑qid

	// 查询用户B的动态，并过滤出用户A可见的
	// 从用户可见动态表查询 每次返回20条，但请求40条，目的是为了防止需要过滤时，额外还要查询数据库的情况(比如根据可见分级未审核的动态需要过滤的情况)
	feeds, err := f.getUserFeedsVisibleTo(targetQid, qid, belowCreatedAt, DBFeedPageCount)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":    qid,
			"cursor": cursor,
		}).Error("GetUserMomentsFeeds 查询用户A查看用户B的可见动态失败")
		utils.InternalError(ctx, "")
		return
	}

	feedMediaIds := make([]int64, 0)
	for _, feed := range feeds {
		for _, media := range feed.Medias {
			feedMediaIds = append(feedMediaIds, media.MediaID)
		}
	}

	feedMediasMap, err := f.getMultiFeedMediasBasicInfo(feedMediaIds)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"qid":          qid,
			"feedMediaIds": feedMediaIds,
		}).Error("GetMomentsFeeds getMultiFeedMediasBasicInfo 查询动态媒体失败")
		utils.InternalError(ctx, "")
		return
	}

	nextCursor := ""
	feedList := make([]MomentsFeed, 0)

	for _, feed := range feeds {
		// TODO 过滤掉关系不对的用户的动态，过滤掉状态不对的动态，过滤可见分级不符的动态

		// 处理位置信息
		var location FeedLocation
		if feed.Location.Valid {
			_ = json.Unmarshal([]byte(feed.Location.String), &location)
		}

		feedUserAvatar := ""  // 从redis获取缓存
		feedUsername := ""    // 从redis获取缓存
		liked := false        // 自己是否已点赞
		likeCount := int64(0) // 总点赞数(包含公域 + 私域)

		// TODO 获取好友点赞和评论
		likes := make([]MomentsFeedDetailLike, 0)       // 所有好友点赞列表
		comments := make([]MomentsFeedDetailComment, 0) // 所有好友评论列表

		// 处理媒体信息
		images, videos := splitMedias(feedMediasMap, feed.Medias)

		// 编码 feed_id feed_qid
		feedIDStr, _ := utils.EncodeFeedID(feed.FeedID)
		feedQidStr, _ := utils.EncodeQID(feed.FeedQid)
		detail := MomentsFeedDetail{
			FeedID:         feedIDStr,
			FeedQid:        feedQidStr,
			FeedUserAvatar: feedUserAvatar,
			FeedUsername:   feedUsername,
			Content:        feed.Content,
			VisType:        f.intVisTypeToString(feed.VisType),
			Public:         feed.Public,
			Location:       location,
			CreatedAt:      feed.CreatedAt,
			UpdateAt:       feed.UpdateAt,
			Liked:          liked,
			LikeCount:      likeCount,
			Images:         images,
			Videos:         videos,
			PinnedFeedID:   "xxx",
		}
		feedList = append(feedList, MomentsFeed{
			MomentsFeedDetail: detail,
			Likes:             likes,
			Comments:          comments,
		})

		nextCursor = fmt.Sprintf("t:%d", feed.CreatedAt)
		if len(feedList) >= 20 {
			break
		}
	}

	// 从数据库中获取不足一页，说明没有更多数据了
	if len(feeds) < DBFeedPageCount {
		nextCursor = ""
	}

	// 把 feedList 中同一天的动态放到一起
	list := make([]DayFeeds, 0)

	response := &GetUserMomentsFeedsResponse{
		NextCursor: nextCursor,
		List:       list,
	}

	utils.SuccessWithMsg(ctx, response, "")
	return
}

// GetExploreFeeds
// 获取用户A公域动态列表
func (f *FeedController) GetExploreFeeds(ctx iris.Context) {
	// TODO 算法待定
}
