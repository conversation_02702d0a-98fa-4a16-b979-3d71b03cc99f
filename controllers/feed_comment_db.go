package controllers

import (
	"database/sql"
	"errors"
	"qing-feeds/database"
	"qing-feeds/models"

	"github.com/lib/pq"
	"github.com/sirupsen/logrus"
)

// checkUserRelationship 检查用户关系（是否为好友）
func (f *FeedController) checkUserRelationship(qid, targetQid int64) (bool, error) {
	// 如果是同一个用户，直接返回true
	if qid == targetQid {
		return true, nil
	}

	db := database.GetDB()
	if db == nil {
		return false, sql.ErrConnDone
	}

	query := `
		SELECT relationship_type
		FROM user_relations
		WHERE qid = $1 AND target_qid = $2
	`

	var relationshipType int
	err := db.QueryRow(query, qid, targetQid).Scan(&relationshipType)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// 没有关系记录，说明是陌生人
			return false, nil
		}
		return false, err
	}

	// 只有好友关系才返回true
	return relationshipType == models.RelationshipTypeFriend, nil
}

// insertComment 插入评论记录
func (f *FeedController) insertComment(commentId, feedId, feedQid, qid int64, content string, parentId, replyToCommentId, replyToQid sql.NullInt64, createdAt int64) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		INSERT INTO feed_comments (comment_id, feed_id, feed_qid, qid, content, parent_id, reply_to_comment_id, reply_to_qid, created_at, status)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
	`

	_, err := db.Exec(query,
		commentId,
		feedId,
		feedQid,
		qid,
		content,
		parentId,
		replyToCommentId,
		replyToQid,
		createdAt,
		0,
	)

	return err
}

// getCommentByID 根据ID查询评论
func (f *FeedController) getCommentByID(commentID int64) (*models.FeedCommentDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT comment_id, feed_id, feed_qid, qid, content, reply_to_comment_id, reply_to_qid, created_at, status
		FROM feed_comments
		WHERE comment_id = $1 AND status = $2
	`

	comment := &models.FeedCommentDBItem{}
	err := db.QueryRow(query, commentID, models.CommentStatusNormal).Scan(
		&comment.CommentID,
		&comment.FeedID,
		&comment.FeedQid,
		&comment.Qid,
		&comment.Content,
		&comment.ReplyToCommentID,
		&comment.ReplyToQid,
		&comment.CreatedAt,
		&comment.Status,
	)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	return comment, nil
}

// getMomentsFeedComments 获取私域动态的评论列表
func (f *FeedController) getMomentsFeedComments(qid, feedID int64, belowCreatedAt int64, limit int64) ([]models.FeedCommentDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	var rows *sql.Rows
	var err error

	if belowCreatedAt > 0 {
		query := `
		SELECT fc.comment_id, fc.feed_id, fc.feed_qid, fc.qid, fc.content, fc.reply_to_comment_id, fc.reply_to_qid, fc.created_at, fc.status
		FROM feed_comments AS fc
		WHERE fc.feed_id = $1 AND fc.created_at < $2 AND fc.status = $3 AND EXISTS (SELECT 1 FROM user_relations WHERE qid = $4 AND target_qid = fc.qid AND relationship_type = $5)
		ORDER BY fc.created_at DESC
		LIMIT $6
	`
		rows, err = db.Query(query, feedID, belowCreatedAt, 0, qid, models.RelationshipTypeFriend, limit)
	} else {
		query := `
		SELECT fc.comment_id, fc.feed_id, fc.feed_qid, fc.qid, fc.content, fc.reply_to_comment_id, fc.reply_to_qid, fc.created_at, fc.status
		FROM feed_comments AS fc
		WHERE fc.feed_id = $1 AND fc.status = $2 AND EXISTS (SELECT 1 FROM user_relations WHERE qid = $3 AND target_qid = fc.qid AND relationship_type = $4)
		ORDER BY fc.created_at DESC
		LIMIT $5
	`
		rows, err = db.Query(query, feedID, 0, qid, models.RelationshipTypeFriend, limit)
	}

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	defer rows.Close()

	commentList := make([]models.FeedCommentDBItem, 0)
	for rows.Next() {
		feed := models.FeedCommentDBItem{}

		if sErr := rows.Scan(
			&feed.CommentID,
			&feed.FeedID,
			&feed.FeedQid,
			&feed.Qid,
			&feed.Content,
			&feed.ReplyToCommentID,
			&feed.ReplyToQid,
			&feed.CreatedAt,
			&feed.Status,
		); sErr != nil {
			logrus.WithError(sErr).Error("getMomentsFeedComments 扫描动态信息失败")
			continue
		}

		commentList = append(commentList, feed)
	}

	return commentList, nil
}

// getMultiMomentsFeedComments 一次获取多个私域动态的评论列表
func (f *FeedController) getMultiMomentsFeedComments(qid int64, feedIDs []int64) (map[int64][]models.FeedCommentDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT fc.comment_id, fc.feed_id, fc.feed_qid, fc.qid, fc.content, fc.reply_to_comment_id, fc.reply_to_qid, fc.created_at, fc.status
		FROM feed_comments AS fc
		WHERE fc.feed_id = ANY($1) AND fc.status = $2 AND EXISTS (SELECT 1 FROM user_relations WHERE qid = $3 AND target_qid = fc.qid AND relationship_type = $4)
		ORDER BY fc.created_at DESC
	`
	rows, err := db.Query(query, pq.Array(feedIDs), 0, qid, models.RelationshipTypeFriend)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	defer rows.Close()

	commentsMap := make(map[int64][]models.FeedCommentDBItem)
	for rows.Next() {
		item := models.FeedCommentDBItem{}

		if sErr := rows.Scan(
			&item.CommentID,
			&item.FeedID,
			&item.FeedQid,
			&item.Qid,
			&item.Content,
			&item.ReplyToCommentID,
			&item.ReplyToQid,
			&item.CreatedAt,
			&item.Status,
		); sErr != nil {
			logrus.WithError(sErr).Error("getMultiMomentsFeedComments 扫描动态评论失败")
			continue
		}
		commentsMap[item.FeedID] = append(commentsMap[item.FeedID], item)
	}

	return commentsMap, nil
}

// getMultiMomentsFeedLikes 一次获取多个私域动态的点赞列表
func (f *FeedController) getMultiMomentsFeedLikes(qid int64, feedIDs []int64) (map[int64][]models.FeedLikeDBItem, error) {
	db := database.GetDB()
	if db == nil {
		return nil, sql.ErrConnDone
	}

	query := `
		SELECT fc.feed_id, fc.feed_qid, fc.qid, fc.created_at
		FROM feed_likes AS fc
		WHERE fc.feed_id = ANY($1) AND EXISTS (SELECT 1 FROM user_relations WHERE qid = $2 AND target_qid = fc.qid AND relationship_type = $3)
		ORDER BY fc.created_at DESC
	`
	rows, err := db.Query(query, pq.Array(feedIDs), qid, models.RelationshipTypeFriend)

	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, nil
		}
		return nil, err
	}

	defer rows.Close()

	commentsMap := make(map[int64][]models.FeedLikeDBItem)
	for rows.Next() {
		item := models.FeedLikeDBItem{}

		if sErr := rows.Scan(
			&item.FeedID,
			&item.FeedQid,
			&item.Qid,
			&item.CreatedAt,
		); sErr != nil {
			logrus.WithError(sErr).Error("getMultiMomentsFeedLikes 扫描动态点赞失败")
			continue
		}
		commentsMap[item.FeedID] = append(commentsMap[item.FeedID], item)
	}

	return commentsMap, nil
}

// getMultiFeedMediasInfo 获取多个动态媒体的基本信息
func (f *FeedController) getMultiFeedMediasBasicInfo(mediaIds []int64) (map[int64]MomentsFeedMediaBasic, error) {
	return nil, nil
}

// getExploreFeedComments 获取公域动态的评论列表
func (f *FeedController) getExploreFeedComments(qid, feedID int64, belowCreatedAt int64, limit int64) ([]models.FeedCommentDBItem, error) {
	return nil, nil
}

// insertLike 插入点赞记录
func (f *FeedController) insertLike(like *FeedLike) error {
	db := database.GetDB()
	if db == nil {
		return sql.ErrConnDone
	}

	query := `
		INSERT INTO feed_likes (feed_id, feed_qid, qid, created_at)
		VALUES ($1, $2, $3, $4)
		ON CONFLICT (feed_id, qid) DO NOTHING
	`

	_, err := db.Exec(query,
		like.FeedID,
		like.FeedQid,
		like.Qid,
		like.CreatedAt,
	)

	return err
}

// checkLikeExists 检查点赞是否已存在
func (f *FeedController) checkLikeExists(feedID, qid int64) (bool, error) {
	db := database.GetDB()
	if db == nil {
		return false, sql.ErrConnDone
	}

	query := `
		SELECT COUNT(*)
		FROM feed_likes
		WHERE feed_id = $1 AND qid = $2
	`

	var count int
	err := db.QueryRow(query, feedID, qid).Scan(&count)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}
