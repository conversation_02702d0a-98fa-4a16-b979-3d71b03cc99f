package controllers

import (
	"qing-feeds/utils"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/sirupsen/logrus"
)

// CreateMomentsFeedLikes
// 点赞私域动态
func (f *FeedController) CreateMomentsFeedLikes(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))
	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("CreateMomentsFeedLikes 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("CreateMomentsFeedLikes 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 验证动态是否存在
	feedDetail, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feedDetail == nil {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("动态不存在")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 检查权限：私域动态需要是好友关系
	if !feedDetail.Public {
		// 如果不是动态发布者本人，需要检查好友关系
		if feedDetail.FeedQid != qid {
			isFriend, err := f.checkUserRelationship(qid, feedDetail.FeedQid)
			if err != nil {
				logrus.WithError(err).WithFields(logrus.Fields{
					"qid":      qid,
					"feed_qid": feedDetail.FeedQid,
				}).Error("检查用户关系失败")
				utils.InternalError(ctx, "")
				return
			}

			if !isFriend {
				utils.InvalidRequestError(ctx, "无权限点赞此动态")
				return
			}
		}
	}

	// 检查是否已经点赞
	exists, err := f.checkLikeExists(feedID, qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("检查点赞状态失败")
		utils.InternalError(ctx, "")
		return
	}

	if exists {
		utils.InvalidRequestError(ctx, "已经点赞过此动态")
		return
	}

	// 创建点赞记录
	now := time.Now().UnixMilli()
	like := &FeedLike{
		FeedID:    feedID,
		FeedQid:   feedDetail.FeedQid,
		Qid:       qid,
		CreatedAt: now,
	}

	if err := f.insertLike(like); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("创建点赞记录失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"feed_id": feedID,
		"qid":     qid,
	}).Info("私域动态点赞成功")

	utils.SuccessWithMsg(ctx, nil, "")
}

// CreateExploreFeedLikes
// 点赞公域动态
func (f *FeedController) CreateExploreFeedLikes(ctx iris.Context) {
	qid, _ := ctx.Values().GetInt64("x-qid")

	feedIDStr := strings.TrimSpace(ctx.Params().Get("feed_id"))
	if feedIDStr == "" {
		logrus.WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
		}).Error("CreateMomentsFeedLikes 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 解码动态ID
	feedID, err := utils.DecodeFeedID(feedIDStr)
	if err != nil || feedID <= 0 {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedIDStr,
			"qid":     qid,
			"feedID":  feedID,
		}).Error("CreateMomentsFeedLikes 无效的动态ID")
		utils.InvalidRequestError(ctx, "")
		return
	}

	// 验证动态是否存在且为公域动态
	feed, err := f.getFeedByID(feedID)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("查询动态失败")
		utils.InternalError(ctx, "")
		return
	}

	if feed == nil {
		utils.InvalidRequestError(ctx, "动态不存在")
		return
	}

	// 公域动态所有人都可以点赞
	if !feed.Public {
		utils.InvalidRequestError(ctx, "此动态不是公域动态")
		return
	}

	// 检查是否已经点赞
	exists, err := f.checkLikeExists(feedID, qid)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("检查点赞状态失败")
		utils.InternalError(ctx, "")
		return
	}

	if exists {
		utils.InvalidRequestError(ctx, "已经点赞过此动态")
		return
	}

	// 创建点赞记录
	now := time.Now().UnixMilli()
	like := &FeedLike{
		FeedID:    feedID,
		FeedQid:   feed.FeedQid,
		Qid:       qid,
		CreatedAt: now,
	}

	if err := f.insertLike(like); err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"feed_id": feedID,
			"qid":     qid,
		}).Error("创建点赞记录失败")
		utils.InternalError(ctx, "")
		return
	}

	logrus.WithFields(logrus.Fields{
		"feed_id": feedID,
		"qid":     qid,
	}).Info("公域动态点赞成功")

	utils.SuccessWithMsg(ctx, nil, "")
}
