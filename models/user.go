package models

import (
	"database/sql"
)

type UserCredentialDBItem struct {
	Qid       int64          `json:"qid"`
	CrdtType  int64          `json:"crdt_type"` // 1:pwd 2:jwt_token
	CrdtKey   string         `json:"crdt_key"`
	CrdtValue string         `json:"crdt_value"`
	CreatedAt int64          `json:"created_at"`
	ExpireAt  int64          `json:"expire_at"`
	Status    int64          `json:"status"`
	Extra     sql.NullString `json:"extra"`
}

// UserBasicInfoDBItem 表示用户基本信息
type UserBasicInfoDBItem struct {
	Qid       int64          `json:"qid"`
	Phone     string         `json:"phone"`
	CustomQid sql.NullString `json:"custom_qid"`
	Username  string         `json:"username"`
	Avatar    string         `json:"avatar"`
	Role      float32        `json:"role"`
	Birthday  int64          `json:"birthday"`
	Region    sql.NullString `json:"region"`
}
