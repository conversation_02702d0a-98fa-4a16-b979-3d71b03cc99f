package models

import (
	"database/sql"
)

type FeedDetailWithMediasDBItem struct {
	FeedDetailDBItem
	Medias []FeedMediaDBItem `json:"medias" db:"medias"`
}

// FeedDetailDBItem 动态表结构
type FeedDetailDBItem struct {
	FeedID    string         `json:"feed_id" db:"feed_id"`
	FeedQid   string         `json:"feed_qid" db:"feed_qid"`
	Content   string         `json:"content" db:"content"`
	VisType   int            `json:"vis_type" db:"vis_type"` // 0:私密 1:公开 2:部分人可见 3:部分人不可见
	Public    bool           `json:"public" db:"public"`
	Location  sql.NullString `json:"location" db:"location"`
	CreatedAt int64          `json:"created_at" db:"created_at"`
	UpdateAt  int64          `json:"update_at" db:"update_at"`
	Status    int            `json:"status" db:"status"` // 0:未审核 1:机审通过 2:人审通过 -1:机审不通过 -2:人审不通过 -3:删除
	Extra     sql.NullString `json:"extra" db:"extra"`
}

// FeedGroup 动态分组表结构
type FeedGroup struct {
	GroupID   string `json:"group_id" db:"group_id"`
	GroupName string `json:"group_name" db:"group_name"`
	OwnerQid  string `json:"owner_qid" db:"owner_qid"`
	GroupType int    `json:"group_type" db:"group_type"` // 0:临时分组 1:固定分组
	CreatedAt int64  `json:"created_at" db:"created_at"`
	UpdateAt  int64  `json:"update_at" db:"update_at"`
	Status    int    `json:"status" db:"status"` // 0:正常 1:删除
}

// FeedGroupMember 分组成员表结构
type FeedGroupMember struct {
	GroupID   string `json:"group_id" db:"group_id"`
	OwnerQid  string `json:"owner_qid" db:"owner_qid"`
	MemberQid string `json:"member_qid" db:"member_qid"`
	CreatedAt int64  `json:"created_at" db:"created_at"`
}

// FeedVisibility 动态可见性表结构
type FeedVisibility struct {
	FeedID    string `json:"feed_id" db:"feed_id"`
	GroupID   string `json:"group_id" db:"group_id"`
	VisType   int    `json:"vis_type" db:"vis_type"` // 0:私密 1:公开 2:白名单 3:黑名单
	CreatedAt int64  `json:"created_at" db:"created_at"`
}

// UserVisibleFeed 用户可见动态表结构
type UserVisibleFeed struct {
	Qid       string `json:"qid" db:"qid"`
	FeedID    string `json:"feed_id" db:"feed_id"`
	FeedQid   string `json:"feed_qid" db:"feed_qid"`
	CreatedAt int64  `json:"created_at" db:"created_at"`
}

// FeedResponse 动态响应
type FeedResponse struct {
	FeedID    string                 `json:"feed_id"`
	FeedQid   string                 `json:"feed_qid"`
	Content   string                 `json:"content"`
	VisType   int                    `json:"vis_type"`
	Location  map[string]interface{} `json:"location,omitempty"`
	CreatedAt int64                  `json:"created_at"`
	UpdateAt  int64                  `json:"update_at"`
	Status    int                    `json:"status"`
	MediaList []FeedMediaDBItem      `json:"media_list,omitempty"`
}

// FeedMediaResponse 动态媒体响应
type FeedMediaResponse struct {
	MediaID            string                 `json:"media_id"`
	MediaType          int                    `json:"media_type"`
	MediaOrder         int                    `json:"media_order"`
	MediaObjectKey     string                 `json:"media_object_key"`
	ThumbnailObjectKey string                 `json:"thumbnail_object_key,omitempty"`
	ThumbHash          string                 `json:"thumbhash,omitempty"`
	FileSize           int                    `json:"file_size"`
	Duration           *int                   `json:"duration,omitempty"`
	Width              int                    `json:"width"`
	Height             int                    `json:"height"`
	Exif               map[string]interface{} `json:"exif,omitempty"`
	Extra              map[string]interface{} `json:"extra,omitempty"`
}

// FeedMediaDBItem 动态媒体表结构
type FeedMediaDBItem struct {
	MediaID        string         `json:"media_id" db:"media_id"`
	FeedID         string         `json:"feed_id" db:"feed_id"`
	FeedQid        string         `json:"feed_qid" db:"feed_qid"`
	MediaType      int            `json:"media_type" db:"media_type"` // 1:图片 2:视频
	MediaOrder     int            `json:"media_order" db:"media_order"`
	MediaObjectKey string         `json:"media_object_key" db:"media_object_key"`
	CreatedAt      int64          `json:"created_at" db:"created_at"`
	ThumbHash      string         `json:"thumb_hash" db:"thumb_hash"`
	FileSize       int            `json:"file_size" db:"file_size"`
	Duration       sql.NullInt32  `json:"duration" db:"duration"` // 视频/音频时长 单位毫秒
	Width          int            `json:"width" db:"width"`
	Height         int            `json:"height" db:"height"`
	Exif           sql.NullString `json:"exif" db:"exif"`
	Extra          sql.NullString `json:"extra" db:"extra"`
}

// FeedListResponse 动态列表响应
type FeedListResponse struct {
	List     []FeedResponse `json:"list"`
	Total    int64          `json:"total"`
	Page     int            `json:"page"`
	PageSize int            `json:"page_size"`
}

// CreateFeedGroupRequest 创建用户组请求
type CreateFeedGroupRequest struct {
	GroupName string   `json:"group_name" validate:"required"`
	GroupType int      `json:"group_type" validate:"required,min=0,max=1"`
	Members   []string `json:"members,omitempty"`
}

// UpdateFeedGroupRequest 修改用户组请求
type UpdateFeedGroupRequest struct {
	GroupName string `json:"group_name" validate:"required"`
}

// FeedGroupResponse 用户组响应
type FeedGroupResponse struct {
	GroupID     string   `json:"group_id"`
	GroupName   string   `json:"group_name"`
	GroupType   int      `json:"group_type"`
	MemberCount int      `json:"member_count"`
	Members     []string `json:"members,omitempty"`
	CreatedAt   int64    `json:"created_at"`
	UpdateAt    int64    `json:"update_at"`
}

// FeedGroupListResponse 用户组列表响应
type FeedGroupListResponse struct {
	List []FeedGroupResponse `json:"list"`
}

// AddMembersRequest 添加成员请求
type AddMembersRequest struct {
	Members []string `json:"members" validate:"required"`
}

// RemoveMembersRequest 移除成员请求
type RemoveMembersRequest struct {
	Members []string `json:"members" validate:"required"`
}

// 可见性类型常量
const (
	VisTypeSelf      = 0 // 私密
	VisTypeAll       = 1 // 公开
	VisTypeWhitelist = 2 // 部分人可见（白名单）
	VisTypeBlacklist = 3 // 部分人不可见（黑名单）
)

// 动态状态常量
// 0:未审核 1:机审通过 2:人审通过 -1:机审不通过 -2:人审不通过 -3:删除
//const (
//	FeedStatusPending  = 0 // 未审核
//	FeedStatusApproved = 1 // 审核通过
//	FeedStatusRejected = 2 // 审核不通过
//	FeedStatusDeleted  = 3 // 删除
//)

// 分组类型常量
const (
	GroupTypeTemporary = 0 // 临时分组
	GroupTypePermanent = 1 // 固定分组
)

const (
	GroupStatusNormal  = 0 // 正常
	GroupStatusDeleted = 1 // 已删除
)

// 媒体类型常量
const (
	MediaTypeImage = 1 // 图片
	MediaTypeVideo = 2 // 视频
)

// 特殊分组ID常量
const (
	GroupIDAll  = "all"  // 公开分组
	GroupIDSelf = "self" // 私密分组
)
