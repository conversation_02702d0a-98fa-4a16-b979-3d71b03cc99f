package models

import (
	"database/sql"
)

// 可见性类型常量
const (
	VisTypeSelf      = 0 // 私密
	VisTypeAll       = 1 // 公开
	VisTypeWhitelist = 2 // 部分人可见（白名单）
	VisTypeBlacklist = 3 // 部分人不可见（黑名单）
)

// 动态状态常量
// 0:未审核 1:机审通过 2:人审通过 -1:机审不通过 -2:人审不通过 -3:删除
//const (
//	FeedStatusPending  = 0 // 未审核
//	FeedStatusApproved = 1 // 审核通过
//	FeedStatusRejected = 2 // 审核不通过
//	FeedStatusDeleted  = 3 // 删除
//)

// 分组类型常量
const (
	GroupTypeTemporary = 0 // 临时分组
	GroupTypePermanent = 1 // 固定分组
)

const (
	GroupStatusNormal  = 0 // 正常
	GroupStatusDeleted = 1 // 已删除
)

// 媒体类型常量
const (
	MediaTypeImage = 1 // 图片
	MediaTypeVideo = 2 // 视频
)

// 特殊分组ID常量
const (
	GroupIDAll  = "all"  // 公开分组
	GroupIDSelf = "self" // 私密分组
)

// 用户关系类型常量
const (
	RelationshipTypeFriend  = 1 // 好友
	RelationshipTypeBlocked = 2 // 拉黑
)

// 评论状态常量
const (
	CommentStatusDeleted = -1 // 删除
	CommentStatusNormal  = 0  // 正常
)

type UserGenMediasDBItem struct {
	MediaId        int64          `json:"media_id"`
	Qid            int64          `json:"qid"`
	Scenario       int            `json:"scenario"`
	MediaObjectKey string         `json:"media_object_key"`
	FileSize       int64          `json:"file_size"`
	Status         int            `json:"status"`
	CreatedAt      int64          `json:"created_at"`
	Extra          sql.NullString `json:"extra"`
}

type FeedDetailWithMediasDBItem struct {
	FeedDetailDBItem
	Medias []FeedMediaDBItem `json:"medias" db:"medias"`
}

// FeedDetailDBItem 动态表结构
type FeedDetailDBItem struct {
	FeedID    int64          `json:"feed_id" db:"feed_id"`
	FeedQid   int64          `json:"feed_qid" db:"feed_qid"`
	Content   string         `json:"content" db:"content"`
	VisType   int            `json:"vis_type" db:"vis_type"` // 0:私密 1:公开 2:部分人可见 3:部分人不可见
	Public    bool           `json:"public" db:"public"`
	Location  sql.NullString `json:"location" db:"location"`
	CreatedAt int64          `json:"created_at" db:"created_at"`
	UpdateAt  int64          `json:"update_at" db:"update_at"`
	Status    int            `json:"status" db:"status"` // 0:未审核 1:机审通过 2:人审通过 -1:机审不通过 -2:人审不通过 -3:删除
	Extra     sql.NullString `json:"extra" db:"extra"`
}

// FeedMediaDBItem 动态媒体表结构
type FeedMediaDBItem struct {
	MediaID        int64          `json:"media_id" db:"media_id"`
	FeedID         int64          `json:"feed_id" db:"feed_id"`
	FeedQid        int64          `json:"feed_qid" db:"feed_qid"`
	MediaType      int            `json:"media_type" db:"media_type"` // 1:图片 2:视频
	MediaOrder     int            `json:"media_order" db:"media_order"`
	MediaObjectKey string         `json:"media_object_key" db:"media_object_key"`
	CreatedAt      int64          `json:"created_at" db:"created_at"`
	ThumbHash      string         `json:"thumb_hash" db:"thumb_hash"`
	FileSize       int            `json:"file_size" db:"file_size"`
	Duration       int64          `json:"duration" db:"duration"` // 视频/音频时长 单位毫秒
	Width          int            `json:"width" db:"width"`
	Height         int            `json:"height" db:"height"`
	Exif           sql.NullString `json:"exif" db:"exif"`
	Extra          sql.NullString `json:"extra" db:"extra"`
}

// FeedCommentDBItem 动态评论表结构
type FeedCommentDBItem struct {
	CommentID        int64         `json:"comment_id" db:"comment_id"`
	FeedID           int64         `json:"feed_id" db:"feed_id"`
	FeedQid          int64         `json:"feed_qid" db:"feed_qid"`
	Qid              int64         `json:"qid" db:"qid"`
	Content          string        `json:"content" db:"content"`
	ReplyToCommentID sql.NullInt64 `json:"reply_to_comment_id" db:"reply_to_comment_id"`
	ReplyToQid       sql.NullInt64 `json:"reply_to_qid" db:"reply_to_qid"`
	CreatedAt        int64         `json:"created_at" db:"created_at"`
	Status           int           `json:"status" db:"status"`
}

type FeedLikeDBItem struct {
	FeedID    int64 `json:"feed_id" db:"feed_id"`
	FeedQid   int64 `json:"feed_qid" db:"feed_qid"`
	Qid       int64 `json:"qid" db:"qid"`
	CreatedAt int64 `json:"created_at" db:"created_at"`
}

// FeedGroupDBItem 动态分组表结构
type FeedGroupDBItem struct {
	GroupID   int64  `json:"group_id" db:"group_id"`
	GroupName string `json:"group_name" db:"group_name"`
	OwnerQid  int64  `json:"owner_qid" db:"owner_qid"`
	GroupType int    `json:"group_type" db:"group_type"` // 0:临时分组 1:固定分组
	CreatedAt int64  `json:"created_at" db:"created_at"`
	UpdateAt  int64  `json:"update_at" db:"update_at"`
	Status    int    `json:"status" db:"status"` // 0:正常 1:删除
}

// FeedGroupMemberDBItem 分组成员表结构
type FeedGroupMemberDBItem struct {
	GroupID   int64 `json:"group_id" db:"group_id"`
	OwnerQid  int64 `json:"owner_qid" db:"owner_qid"`
	MemberQid int64 `json:"member_qid" db:"member_qid"`
	CreatedAt int64 `json:"created_at" db:"created_at"`
}
