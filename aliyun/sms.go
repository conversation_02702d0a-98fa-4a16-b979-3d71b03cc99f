package aliyun

import (
	"log"
	"qing-feeds/config"

	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	dysmsapi "github.com/alibabacloud-go/dysmsapi-20170525/v2/client"
	util "github.com/alibabacloud-go/tea-utils/service"
	"github.com/alibabacloud-go/tea/tea"
)

var smsClient *dysmsapi.Client
var signName = ""
var templateCode = ""

func init() {
	cfg := config.GetConfig()
	client, _err := createSmsClient(tea.String(cfg.Sms.AccessKeyId), tea.String(cfg.Sms.AccessKeySecret))
	if _err != nil {
		log.Fatalf("create aliyun client error: %v", _err)
		return
	}
	smsClient = client
	signName = cfg.Sms.SignName
	templateCode = cfg.Sms.TemplateCode
}

func createSmsClient(accessKeyId *string, accessKeySecret *string) (_result *dysmsapi.Client, _err error) {
	config := &openapi.Config{}
	config.AccessKeyId = accessKeyId
	config.AccessKeySecret = accessKeySecret
	_result = &dysmsapi.Client{}
	_result, _err = dysmsapi.NewClient(config)
	return _result, _err
}

func SendSmsOtp(phone, otp string) error {
	sendReq := &dysmsapi.SendSmsRequest{
		PhoneNumbers:  tea.String(phone),
		SignName:      tea.String(signName),
		TemplateCode:  tea.String(templateCode),
		TemplateParam: tea.String("{\"code\":\"" + otp + "\"}"),
	}
	sendResp, _err := smsClient.SendSms(sendReq)
	if _err != nil {
		return _err
	}

	code := sendResp.Body.Code
	if !tea.BoolValue(util.EqualString(code, tea.String("OK"))) {
		log.Printf("SendSmsOtp 错误信息: %v", tea.StringValue(sendResp.Body.Message))
		return _err
	}

	return nil
}
