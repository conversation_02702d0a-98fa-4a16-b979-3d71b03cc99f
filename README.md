# Qing Feeds - 社交动态模块

这是一个基于Go语言和Iris框架开发的社交软件动态模块服务端项目。

## 功能特性

### 动态管理
- ✅ 发布动态（支持文本、图片、视频、位置信息）
- ✅ 修改动态（内容和可见性）
- ✅ 删除动态（软删除）
- ✅ 查看动态详情
- ✅ 获取动态列表（分页）
- ✅ 查看指定用户的可见动态

### 可见性控制
支持四种可见性类型：
- **公开** (0): 全部好友可见
- **私密** (1): 仅自己可见  
- **部分人可见** (2): 白名单模式，只有指定用户组成员可见
- **部分人不可见** (3): 黑名单模式，指定用户组成员不可见

### 用户组管理
- ✅ 创建用户组（临时/固定分组）
- ✅ 删除用户组
- ✅ 修改用户组名称
- ✅ 批量添加/移除用户组成员
- ✅ 获取用户组列表

### 技术特性
- 🔥 Snowflake算法生成唯一ID
- 🔥 异步处理用户可见性计算
- 🔥 支持媒体文件管理（图片/视频）
- 🔥 完整的数据库事务支持
- 🔥 RESTful API设计
- 🔥 完善的错误处理和日志记录

## 技术栈

- **语言**: Go 1.19+
- **框架**: Iris Web Framework
- **数据库**: PostgreSQL
- **缓存**: Redis
- **认证**: JWT
- **日志**: Logrus
- **ID生成**: Snowflake算法

## 项目结构

```
qing-feeds/
├── controllers/          # 控制器层
│   ├── feed.go          # 动态相关接口
│   ├── feed_db.go       # 动态数据库操作
│   ├── feed_group.go    # 用户组相关接口
│   ├── feed_group_db.go # 用户组数据库操作
│   ├── common.go        # 公共方法
│   ├── health.go        # 健康检查
│   └── profile.go       # 用户资料
├── models/              # 数据模型
│   ├── feed.go         # 动态相关模型
│   └── user.go         # 用户相关模型
├── utils/               # 工具包
│   ├── snowflake.go    # Snowflake ID生成器
│   ├── api_client.go   # API客户端封装
│   ├── response.go     # 响应格式化
│   ├── jwt.go          # JWT工具
│   └── common.go       # 通用工具
├── database/            # 数据库连接
│   ├── postgres.go     # PostgreSQL连接
│   └── redis.go        # Redis连接
├── middleware/          # 中间件
│   └── headers.go      # 请求头处理
├── config/             # 配置管理
│   └── config.go       # 配置结构
├── database_schema.sql # 数据库表结构
├── API_DOCUMENTATION.md # API文档
├── test_api.sh         # API测试脚本
└── main.go             # 程序入口
```

## 数据库设计

### 核心表结构

1. **feeds** - 动态表
2. **user_feed_groups** - 用户分组表  
3. **user_feed_group_members** - 分组成员表
4. **feed_visibilities** - 动态可见性表
5. **user_visible_feeds** - 用户可见动态表
6. **feed_medias** - 动态媒体表

详细的表结构请参考 `database_schema.sql` 文件。

## 快速开始

### 1. 环境准备

确保已安装：
- Go 1.19+
- PostgreSQL 12+
- Redis 6+

### 2. 数据库初始化

```bash
# 连接到PostgreSQL数据库
psql -h your_host -U your_user -d your_database

# 执行建表脚本
\i database_schema.sql
```

### 3. 配置环境

修改 `config/config.go` 中的数据库连接信息。

### 4. 编译运行

```bash
# 编译
go build -o bin/qing-feeds

# 运行
./bin/qing-feeds
```

服务将在 `http://localhost:8002` 启动。

### 5. API测试

```bash
# 修改test_api.sh中的JWT_TOKEN
vim test_api.sh

# 执行测试
./test_api.sh
```

## API接口

### 动态相关
- `POST /api/feeds` - 发布动态
- `PUT /api/feeds/{feed_id}` - 修改动态  
- `DELETE /api/feeds/{feed_id}` - 删除动态
- `GET /api/feeds` - 获取动态列表
- `GET /api/feeds/user/{qid}` - 获取用户动态
- `GET /api/feeds/{feed_id}` - 查看动态详情

### 用户组相关
- `POST /api/feed-groups` - 创建用户组
- `DELETE /api/feed-groups/{group_id}` - 删除用户组
- `PUT /api/feed-groups/{group_id}` - 修改用户组
- `POST /api/feed-groups/{group_id}/members` - 添加成员
- `DELETE /api/feed-groups/{group_id}/members` - 移除成员  
- `GET /api/feed-groups` - 获取用户组列表

详细的API文档请参考 `API_DOCUMENTATION.md`。

## 核心实现逻辑

### 动态发布流程

1. **验证请求参数** - 检查内容、可见性类型、用户组等
2. **生成动态ID** - 使用Snowflake算法生成唯一ID
3. **数据库事务** - 原子性插入动态、可见性、媒体记录
4. **异步处理** - 提交用户可见性计算任务

### 可见性控制

根据不同的可见性类型，在 `feed_visibilities` 表中插入相应记录：

- **公开**: `group_id = "all"`
- **私密**: `group_id = "self"`  
- **白名单**: 为每个选中的用户组插入记录
- **黑名单**: 为每个选中的用户组插入记录

### 异步处理

动态发布后，通过API调用异步服务计算用户可见性，避免阻塞主流程。

## 注意事项

1. **认证要求** - 所有API都需要有效的JWT token
2. **异步处理** - 动态发布后可见性计算是异步的
3. **软删除** - 删除操作不会真正删除数据
4. **事务保证** - 关键操作都使用数据库事务
5. **分页查询** - 列表接口都支持分页参数

## 开发计划

- [ ] 添加动态点赞/评论功能
- [ ] 实现动态搜索功能  
- [ ] 添加敏感内容检测
- [ ] 优化查询性能
- [ ] 添加缓存机制
- [ ] 完善单元测试

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
